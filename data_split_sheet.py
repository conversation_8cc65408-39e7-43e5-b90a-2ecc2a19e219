# import pandas as pd
# from sklearn.model_selection import train_test_split

# def split_excel_for_bert_3way(file_path, text_column, label_column,
#                                train_path, val_path, test_path):
#     # Load all sheets
#     xls = pd.ExcelFile(file_path)
#     sheet_names = xls.sheet_names
#     all_dfs = [pd.read_excel(xls, sheet_name=sheet) for sheet in sheet_names]
#     df = pd.concat(all_dfs, ignore_index=True)

#     # Clean data
#     df = df.dropna(subset=[text_column, label_column])
#     df[text_column] = df[text_column].astype(str).str.strip()
#     df = df[df[text_column] != '']

#     # Optional: Remove classes with only one sample
#     label_counts = df[label_column].value_counts()
#     df = df[df[label_column].isin(label_counts[label_counts > 1].index)]

#     # First split: 60% train, 40% temp
#     train_df, temp_df = train_test_split(
#         df, test_size=0.4, random_state=42, stratify=df[label_column]
#     )

#     # Second split: 20% val, 20% test from temp
#     val_df, test_df = train_test_split(
#         temp_df, test_size=0.5, random_state=42, stratify=temp_df[label_column]
#     )

#     # Save to Excel
#     train_df.to_excel(train_path, index=False, engine='openpyxl')
#     val_df.to_excel(val_path, index=False, engine='openpyxl')
#     test_df.to_excel(test_path, index=False, engine='openpyxl')

#     # Print summary
#     print(f"✅ Train: {len(train_df)} rows → {train_path}")
#     print(f"✅ Validation: {len(val_df)} rows → {val_path}")
#     print(f"✅ Test: {len(test_df)} rows → {test_path}")

# # 🔍 Example usage
# split_excel_for_bert_3way(
#     file_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/all_merge_new.xlsx",
#     text_column="labor_description_str",
#     label_column="category_num",
#     train_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train_data_join.xlsx",
#     val_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/val_data_join.xlsx",
#     test_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/test_data_join.xlsx"
# )
#############################################
# import pandas as pd
# from sklearn.model_selection import train_test_split

# def split_excel_three_way(file_path, sheet_names, text_column, label_column,
#                           train_path, val_path, test_path):
#     # Read and merge all sheets
#     all_dfs = []
#     for sheet in sheet_names:
#         df = pd.read_excel(file_path, sheet_name=sheet)
#         all_dfs.append(df)

#     merged_df = pd.concat(all_dfs, ignore_index=True)
#     print(f"✅ Total merged rows: {len(merged_df)}")

#     # Clean data
#     merged_df = merged_df.dropna(subset=[text_column, label_column])
#     merged_df[text_column] = merged_df[text_column].astype(str).str.strip()
#     merged_df = merged_df[merged_df[text_column] != '']

#     # Remove labels with only 1 instance (BERT won't handle stratified split well)
#     label_counts = merged_df[label_column].value_counts()
#     merged_df = merged_df[merged_df[label_column].isin(label_counts[label_counts > 1].index)]

#     print(f"✅ Rows after cleaning: {len(merged_df)}")

#     # Split into Train (60%) and Temp (40%)
#     train_df, temp_df = train_test_split(
#         merged_df, test_size=0.4, stratify=merged_df[label_column], random_state=42
#     )

#     # Split Temp (40%) into Validation (20%) and Test (20%)
#     val_df, test_df = train_test_split(
#         temp_df, test_size=0.5, stratify=temp_df[label_column], random_state=42
#     )

#     # Save to Excel
#     train_df.to_excel(train_path, index=False, engine='openpyxl')
#     val_df.to_excel(val_path, index=False, engine='openpyxl')
#     test_df.to_excel(test_path, index=False, engine='openpyxl')

#     # Print results
#     print(f"\n✅ Train saved: {train_path} — {len(train_df)} rows")
#     print(f"✅ Validation saved: {val_path} — {len(val_df)} rows")
#     print(f"✅ Test saved: {test_path} — {len(test_df)} rows")

# # Example usage
# split_excel_three_way(
#     file_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/all_merge_new.xlsx",
#     sheet_names=["Merged_Part_1", "Merged_Part_2", "Merged_Part_3"],  # Replace with actual sheet names if different
#     text_column="labor_description_str",
#     label_column="category_num",
#     train_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train_data_join.xlsx",
#     val_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/val_data_join.xlsx",
#     test_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/test_data_join.xlsx"
# )

###########################
import pandas as pd
from sklearn.model_selection import train_test_split

def split_excel_for_bert(file_path, text_column, label_column,
                         train_path, val_path, test_path):
    # Load all sheets
    xls = pd.ExcelFile(file_path)
    sheet_names = xls.sheet_names

    # Combine all sheets
    dfs = [pd.read_excel(xls, sheet_name=sheet) for sheet in sheet_names]
    merged_df = pd.concat(dfs, ignore_index=True)

    # Clean missing values
    merged_df = merged_df.dropna(subset=[text_column, label_column])
    merged_df[text_column] = merged_df[text_column].astype(str).str.strip()
    merged_df = merged_df[merged_df[text_column] != '']

    print(f"✅ Total cleaned rows: {len(merged_df)}")

    # 60% train, 20% val, 20% test
    train_df, temp_df = train_test_split(merged_df, test_size=0.4, random_state=42)
    val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42)

    print(f"✅ Train: {len(train_df)} rows")
    print(f"✅ Validation: {len(val_df)} rows")
    print(f"✅ Test: {len(test_df)} rows")

    # Save to Excel
    train_df.to_excel(train_path, index=False, engine='openpyxl')
    val_df.to_excel(val_path, index=False, engine='openpyxl')
    test_df.to_excel(test_path, index=False, engine='openpyxl')

    print(f"📁 Saved: \n - {train_path} \n - {val_path} \n - {test_path}")


# 🔧 Example usage:
split_excel_for_bert(
    file_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/all_merge_new.xlsx",
    text_column="labor_description_str",
    label_column="category_num",
    train_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train_data_60.xlsx",
    val_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/val_data_20.xlsx",
    test_path="/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/test_data_20.xlsx"
)
