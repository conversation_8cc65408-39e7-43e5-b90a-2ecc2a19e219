# ============================================================================
# KAGGLE NOTEBOOK CELLS FOR BERT TRAINING WITH PRE-SPLIT DATA
# Copy each cell block into separate Kaggle notebook cells
# ============================================================================

# CELL 1: Install and Import Required Libraries
# ============================================================================
"""
Install and import all required libraries for BERT training
"""

# Install required packages (if needed)
# !pip install transformers torch scikit-learn tqdm

import pandas as pd
import numpy as np
import torch
import os
import warnings
import pickle
import json
from collections import Counter
from pathlib import Path

# Kaggle-friendly settings
warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import transformers and other ML libraries
from torch.utils.data import Dataset, DataLoader
from transformers import (
    BertTokenizer, 
    BertForSequenceClassification, 
    get_linear_schedule_with_warmup,
    AutoTokenizer,
    AutoModelForSequenceClassification
)
from torch.optim import AdamW
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, f1_score
from tqdm.auto import tqdm

print("✅ All libraries imported successfully!")
print(f"🖥️ PyTorch version: {torch.__version__}")
print(f"🔥 CUDA available: {torch.cuda.is_available()}")

# ============================================================================
# CELL 2: Dataset Class and Helper Functions
# ============================================================================
"""
Define dataset class and helper functions for data processing
"""

class LaborDataset(Dataset):
    """Dataset class for labor descriptions"""
    
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def load_split_data():
    """Load pre-split train/validation/test data from Kaggle input"""
    
    # Kaggle input paths
    train_file = "/kaggle/input/your-dataset/train_data_60.xlsx"  # Update with your dataset name
    val_file = "/kaggle/input/your-dataset/val_data_20.xlsx"      # Update with your dataset name
    test_file = "/kaggle/input/your-dataset/test_data_20.xlsx"    # Update with your dataset name
    
    print("📁 Loading pre-split data files...")
    
    # Load datasets
    train_df = pd.read_excel(train_file)
    val_df = pd.read_excel(val_file)
    test_df = pd.read_excel(test_file)
    
    print(f"✅ Train data: {train_df.shape[0]} samples")
    print(f"✅ Validation data: {val_df.shape[0]} samples")
    print(f"✅ Test data: {test_df.shape[0]} samples")
    
    return train_df, val_df, test_df

def prepare_data_for_training(train_df, val_df, test_df):
    """Prepare and clean data for training"""
    
    # Check required columns
    required_cols = ['labor_description_str', 'category_num']
    
    # Clean data
    print("🧹 Cleaning data...")
    
    def clean_dataframe(df):
        df = df.dropna(subset=required_cols)
        df['labor_description_str'] = df['labor_description_str'].astype(str)
        df = df[df['labor_description_str'].str.strip() != '']
        return df
    
    train_df = clean_dataframe(train_df)
    val_df = clean_dataframe(val_df)
    test_df = clean_dataframe(test_df)
    
    # Get unique classes
    all_classes = set(train_df['category_num'].unique()) | set(val_df['category_num'].unique()) | set(test_df['category_num'].unique())
    
    print(f"📈 Total unique classes: {len(all_classes)}")
    print(f"🏷️ Classes: {sorted(all_classes)}")
    
    # Create label mapping (0-indexed)
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(sorted(all_classes))}
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    # Apply label mapping
    train_df['mapped_labels'] = train_df['category_num'].map(label_mapping)
    val_df['mapped_labels'] = val_df['category_num'].map(label_mapping)
    test_df['mapped_labels'] = test_df['category_num'].map(label_mapping)
    
    # Get category mapping if available
    category_mapping = None
    if 'labor_category_label' in train_df.columns:
        category_mapping = train_df[['labor_category_label', 'category_num']].drop_duplicates().sort_values('category_num')
        print("\n🏷️ Category Mapping:")
        for _, row in category_mapping.iterrows():
            mapped_label = label_mapping.get(row['category_num'], 'Unknown')
            print(f"  {row['category_num']} → {mapped_label}: {row['labor_category_label']}")
    
    return train_df, val_df, test_df, len(all_classes), label_mapping, reverse_mapping, category_mapping

def monitor_gpu_memory():
    """Monitor and display GPU memory usage"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        total = torch.cuda.get_device_properties(0).total_memory / 1024**3

        print(f"🖥️ GPU Memory Status:")
        print(f"  Allocated: {allocated:.2f}GB")
        print(f"  Reserved: {reserved:.2f}GB")
        print(f"  Total: {total:.2f}GB")
        print(f"  Free: {total - reserved:.2f}GB")

        return allocated, reserved, total
    else:
        print("🖥️ No GPU available")
        return 0, 0, 0

def clear_gpu_cache_aggressive():
    """Aggressively clear GPU cache and collect garbage"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        import gc
        gc.collect()
        print("🧹 Aggressive GPU cache clearing completed")

print("✅ Helper functions defined!")

# ============================================================================
# CELL 3: Load and Prepare Data
# ============================================================================
"""
Load and prepare the pre-split data for training
"""

# Load the data
train_df, val_df, test_df = load_split_data()

# Prepare data for training
train_df, val_df, test_df, num_classes, label_mapping, reverse_mapping, category_mapping = prepare_data_for_training(
    train_df, val_df, test_df
)

print(f"\n📊 Data Summary:")
print(f"  Training samples: {len(train_df)}")
print(f"  Validation samples: {len(val_df)}")
print(f"  Test samples: {len(test_df)}")
print(f"  Number of classes: {num_classes}")

# ============================================================================
# CELL 4: Model Training Function
# ============================================================================
"""
Define the BERT training function with validation
"""

def train_bert_model_with_validation(train_df, val_df, num_classes, label_mapping,
                                   batch_size=8, epochs=3, learning_rate=2e-5, 
                                   model_name='bert-base-uncased'):
    """Train BERT model with separate validation set"""
    
    print(f"\n{'='*60}")
    print("🚀 BERT MODEL TRAINING WITH VALIDATION")
    print(f"{'='*60}")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    if torch.cuda.is_available():
        print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Load model and tokenizer
    print(f"📥 Loading {model_name}...")
    
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name, 
        num_labels=num_classes
    ).to(device)
    print("✅ Model loaded successfully")
    
    # Prepare data
    X_train = train_df['labor_description_str'].tolist()
    y_train = train_df['mapped_labels'].tolist()
    X_val = val_df['labor_description_str'].tolist()
    y_val = val_df['mapped_labels'].tolist()
    
    print(f"📚 Training samples: {len(X_train)}")
    print(f"🔍 Validation samples: {len(X_val)}")
    
    # Create datasets
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    val_dataset = LaborDataset(X_val, y_val, tokenizer)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer
    optimizer = AdamW(model.parameters(), lr=learning_rate)
    total_steps = len(train_loader) * epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer, num_warmup_steps=0, num_training_steps=total_steps
    )
    
    # Training loop
    train_losses = []
    val_accuracies = []

    print(f"\n🎯 Starting training for {epochs} epoch(s)...")

    for epoch in range(epochs):
        print(f"\n📖 Epoch {epoch + 1}/{epochs}")

        # Clear GPU cache at the start of each epoch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print(f"🧹 GPU cache cleared. Memory allocated: {torch.cuda.memory_allocated()/1024**3:.1f}GB")

        # Training phase
        model.train()
        total_loss = 0
        batch_count = 0

        progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch + 1}")

        for batch_idx, batch in enumerate(progress_bar):
            optimizer.zero_grad()

            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)

            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )

            loss = outputs.loss
            loss.backward()
            optimizer.step()
            scheduler.step()

            total_loss += loss.item()
            batch_count += 1

            # Memory management - clear cache periodically
            if batch_idx % 100 == 0 and batch_idx > 0 and torch.cuda.is_available():
                torch.cuda.empty_cache()

            # Update progress with memory info
            if torch.cuda.is_available():
                progress_bar.set_postfix({
                    'loss': f'{loss.item():.4f}',
                    'mem': f'{torch.cuda.memory_allocated()/1024**3:.1f}GB'
                })
            else:
                progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})

            # Checkpoint every 1000 batches to prevent loss of progress
            if batch_idx % 1000 == 0 and batch_idx > 0:
                checkpoint_path = f"/kaggle/working/checkpoint_epoch{epoch+1}_batch{batch_idx}"
                os.makedirs(checkpoint_path, exist_ok=True)
                model.save_pretrained(checkpoint_path)
                tokenizer.save_pretrained(checkpoint_path)
                print(f"\n💾 Checkpoint saved at epoch {epoch+1}, batch {batch_idx}")

                # Clear cache after checkpoint
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

        avg_loss = total_loss / len(train_loader)
        train_losses.append(avg_loss)
        print(f"📉 Average training loss: {avg_loss:.4f}")

        # Clear GPU cache after training phase
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print(f"🧹 Post-training GPU cache cleared")
        
        # Validation phase
        model.eval()
        val_predictions = []
        val_true_labels = []

        # Clear cache before validation
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        with torch.no_grad():
            val_progress = tqdm(val_loader, desc="Validation")
            for batch_idx, batch in enumerate(val_progress):
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                labels = batch['labels'].to(device)

                outputs = model(input_ids=input_ids, attention_mask=attention_mask)
                logits = outputs.logits

                val_predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
                val_true_labels.extend(labels.cpu().numpy())

                # Clear cache periodically during validation
                if batch_idx % 50 == 0 and batch_idx > 0 and torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # Update progress with memory info
                if torch.cuda.is_available():
                    val_progress.set_postfix({
                        'mem': f'{torch.cuda.memory_allocated()/1024**3:.1f}GB'
                    })

        val_accuracy = accuracy_score(val_true_labels, val_predictions)
        val_accuracies.append(val_accuracy)
        print(f"🎯 Validation Accuracy: {val_accuracy:.4f}")

        # Clear cache after validation
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print(f"🧹 Post-validation GPU cache cleared")

        # Save epoch checkpoint
        epoch_checkpoint_path = f"/kaggle/working/checkpoint_epoch{epoch+1}_complete"
        os.makedirs(epoch_checkpoint_path, exist_ok=True)
        model.save_pretrained(epoch_checkpoint_path)
        tokenizer.save_pretrained(epoch_checkpoint_path)

        # Save training progress
        progress_data = {
            'epoch': epoch + 1,
            'train_losses': train_losses,
            'val_accuracies': val_accuracies,
            'label_mapping': label_mapping
        }
        with open(f"{epoch_checkpoint_path}/training_progress.json", 'w') as f:
            json.dump(progress_data, f, indent=2)

        print(f"💾 Epoch {epoch+1} checkpoint saved to: {epoch_checkpoint_path}")

        # Final cache clear for the epoch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    return model, tokenizer, train_losses, val_accuracies, val_accuracy

print("✅ Training function defined!")

# ============================================================================
# CELL 5: Memory-Optimized Training Configuration
# ============================================================================
"""
Configure training parameters with memory optimization for Kaggle
"""

# Memory-optimized training parameters for Kaggle
if torch.cuda.is_available():
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f"🔥 GPU Memory Available: {gpu_memory:.1f}GB")

    # Adjust batch size based on GPU memory
    if gpu_memory >= 15:  # High-end GPU
        BATCH_SIZE = 16
    elif gpu_memory >= 10:  # Mid-range GPU
        BATCH_SIZE = 8
    else:  # Lower memory GPU
        BATCH_SIZE = 4
else:
    BATCH_SIZE = 2  # Very conservative for CPU

EPOCHS = 3
LEARNING_RATE = 2e-5

# Additional memory optimization settings
MAX_LENGTH = 128  # Reduce if memory issues persist
GRADIENT_ACCUMULATION_STEPS = 1  # Increase if need larger effective batch size

print("🚀 Memory-Optimized Training Configuration:")
print(f"  Batch Size: {BATCH_SIZE}")
print(f"  Epochs: {EPOCHS}")
print(f"  Learning Rate: {LEARNING_RATE}")
print(f"  Max Sequence Length: {MAX_LENGTH}")
print(f"  Gradient Accumulation: {GRADIENT_ACCUMULATION_STEPS}")

# Clear any existing cache
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print(f"🧹 Initial GPU cache cleared")

# ============================================================================
# CELL 6: Start Training with Memory Management
# ============================================================================
"""
Start the BERT model training process with comprehensive memory management
"""

# Train the model
model, tokenizer, train_losses, val_accuracies, final_val_accuracy = train_bert_model_with_validation(
    train_df, val_df, num_classes, label_mapping,
    batch_size=BATCH_SIZE,
    epochs=EPOCHS,
    learning_rate=LEARNING_RATE
)

print(f"\n✅ Training completed!")
print(f"📊 Final validation accuracy: {final_val_accuracy:.4f}")

# ============================================================================
# CELL 6: Model Evaluation and Testing
# ============================================================================
"""
Evaluate the trained model on the test set
"""

def evaluate_model_on_test(model, tokenizer, test_df, label_mapping, reverse_mapping, category_mapping, device):
    """Evaluate model on test set and generate comprehensive report"""

    print(f"\n{'='*60}")
    print("🧪 FINAL MODEL EVALUATION ON TEST SET")
    print(f"{'='*60}")

    model.eval()

    # Prepare test data
    X_test = test_df['labor_description_str'].tolist()
    y_test = test_df['mapped_labels'].tolist()

    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)

    # Get predictions
    predictions = []
    true_labels = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)

            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits

            predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())

    # Calculate metrics
    accuracy = accuracy_score(true_labels, predictions)
    f1_macro = f1_score(true_labels, predictions, average='macro')
    f1_weighted = f1_score(true_labels, predictions, average='weighted')

    print(f"\n🎯 Test Results:")
    print(f"📊 Accuracy: {accuracy:.4f}")
    print(f"📊 F1 Score (Macro): {f1_macro:.4f}")
    print(f"📊 F1 Score (Weighted): {f1_weighted:.4f}")

    # Generate classification report
    target_names = None
    if category_mapping is not None:
        try:
            target_names = []
            for i in range(len(label_mapping)):
                original_label = reverse_mapping[i]
                category_row = category_mapping[category_mapping['category_num'] == original_label]
                if not category_row.empty:
                    category_name = category_row['labor_category_label'].iloc[0]
                    target_names.append(f"Class_{original_label}_{category_name}")
                else:
                    target_names.append(f"Class_{original_label}")
        except:
            target_names = [f"Class_{reverse_mapping[i]}" for i in range(len(label_mapping))]
    else:
        target_names = [f"Class_{reverse_mapping[i]}" for i in range(len(label_mapping))]

    # Classification report
    class_report = classification_report(true_labels, predictions, target_names=target_names, output_dict=True)
    class_report_str = classification_report(true_labels, predictions, target_names=target_names)

    print("\n📊 Detailed Classification Report:")
    print(class_report_str)

    # Save classification report to Kaggle working directory
    with open("/kaggle/working/classification_report.txt", 'w') as f:
        f.write("BERT Labor Description Classification Report\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Test Accuracy: {accuracy:.4f}\n")
        f.write(f"F1 Score (Macro): {f1_macro:.4f}\n")
        f.write(f"F1 Score (Weighted): {f1_weighted:.4f}\n\n")
        f.write("Detailed Classification Report:\n")
        f.write(class_report_str)

    print(f"✅ Classification report saved to: /kaggle/working/classification_report.txt")

    return accuracy, f1_macro, f1_weighted, class_report

# Run evaluation
device = next(model.parameters()).device
test_accuracy, test_f1_macro, test_f1_weighted, class_report = evaluate_model_on_test(
    model, tokenizer, test_df, label_mapping, reverse_mapping, category_mapping, device
)

# ============================================================================
# CELL 7: Save Model and Generate Reports
# ============================================================================
"""
Save the trained model and generate comprehensive reports for Kaggle
"""

# Save model to Kaggle working directory
model_save_path = "/kaggle/working/bert_labor_classifier_final"
os.makedirs(model_save_path, exist_ok=True)

# Save model and tokenizer
model.save_pretrained(model_save_path)
tokenizer.save_pretrained(model_save_path)

print(f"✅ Model saved to: {model_save_path}")

# Save comprehensive metadata
metadata = {
    'model_info': {
        'model_type': 'BERT',
        'model_name': 'bert_labor_classifier_final',
        'num_classes': len(label_mapping),
        'training_approach': 'Pre-split data (60:20:20)'
    },
    'performance_metrics': {
        'test_accuracy': test_accuracy,
        'test_f1_macro': test_f1_macro,
        'test_f1_weighted': test_f1_weighted,
        'final_validation_accuracy': val_accuracies[-1] if val_accuracies else None
    },
    'training_history': {
        'train_losses': train_losses,
        'validation_accuracies': val_accuracies
    },
    'label_mappings': {
        'original_to_model': label_mapping,
        'model_to_original': reverse_mapping
    }
}

# Save metadata as JSON
with open("/kaggle/working/model_metadata.json", 'w') as f:
    json.dump(metadata, f, indent=2)

# Save category mapping if available
if category_mapping is not None:
    category_mapping.to_csv("/kaggle/working/category_mapping.csv", index=False)

# Save model data as pickle
model_data = {
    'label_mapping': label_mapping,
    'reverse_mapping': reverse_mapping,
    'category_mapping': category_mapping,
    'test_accuracy': test_accuracy,
    'test_f1_macro': test_f1_macro,
    'test_f1_weighted': test_f1_weighted,
    'train_losses': train_losses,
    'validation_accuracies': val_accuracies
}

with open("/kaggle/working/model_data.pkl", 'wb') as f:
    pickle.dump(model_data, f)

# Create comprehensive summary
with open("/kaggle/working/model_summary.txt", 'w') as f:
    f.write("BERT Labor Description Classifier - Training Summary\n")
    f.write("=" * 55 + "\n\n")
    f.write(f"Model Type: BERT (bert-base-uncased)\n")
    f.write(f"Number of Classes: {len(label_mapping)}\n")
    f.write(f"Training Approach: Pre-split data (60:20:20)\n\n")
    f.write("Performance Metrics:\n")
    f.write(f"  Test Accuracy: {test_accuracy:.4f}\n")
    f.write(f"  Test F1 Score (Macro): {test_f1_macro:.4f}\n")
    f.write(f"  Test F1 Score (Weighted): {test_f1_weighted:.4f}\n\n")
    f.write(f"Training History:\n")
    f.write(f"  Training Losses: {train_losses}\n")
    f.write(f"  Validation Accuracies: {val_accuracies}\n\n")
    f.write(f"Model saved to: {model_save_path}\n")
    f.write(f"Files available for download in Kaggle working directory\n")

print("✅ All reports and metadata saved!")

# ============================================================================
# CELL 8: Test Sample Predictions
# ============================================================================
"""
Test the model with sample predictions to verify it's working correctly
"""

def test_sample_predictions(model, tokenizer, label_mapping, reverse_mapping, category_mapping, device):
    """Test model with sample predictions"""

    print(f"\n{'='*60}")
    print("🧪 TESTING SAMPLE PREDICTIONS")
    print(f"{'='*60}")

    model.eval()

    test_descriptions = [
        "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
        "BATTERY TEST AND REPLACEMENT",
        "BRAKE PAD REPLACEMENT",
        "TIRE ROTATION AND BALANCE",
        "DIAGNOSIS OF ENGINE PROBLEM",
        "WIPER BLADE REPLACEMENT",
        "AIR FILTER REPLACEMENT",
        "TRANSMISSION FLUID CHANGE"
    ]

    for desc in test_descriptions:
        encoding = tokenizer(
            desc,
            truncation=True,
            padding='max_length',
            max_length=128,
            return_tensors='pt'
        ).to(device)

        with torch.no_grad():
            outputs = model(**encoding)
            logits = outputs.logits
            predicted_class = torch.argmax(logits, dim=-1).item()
            confidence = torch.softmax(logits, dim=-1).max().item()

        # Map back to original label
        original_label = reverse_mapping[predicted_class]

        # Get category name if available
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                category_display = f"{original_label} - {category_name}"
            except:
                category_display = str(original_label)
        else:
            category_display = str(original_label)

        print(f"🔧 Description: {desc}")
        print(f"🏷️ Predicted: {category_display}")
        print(f"📊 Confidence: {confidence:.4f}")
        print("-" * 50)

# Run sample predictions
test_sample_predictions(model, tokenizer, label_mapping, reverse_mapping, category_mapping, device)

# ============================================================================
# FINAL SUMMARY
# ============================================================================
print(f"\n🎉 TRAINING COMPLETED SUCCESSFULLY!")
print(f"📊 Final Test Accuracy: {test_accuracy:.4f}")
print(f"📊 Final Test F1 (Macro): {test_f1_macro:.4f}")
print(f"📊 Final Test F1 (Weighted): {test_f1_weighted:.4f}")
print(f"💾 Model saved to: {model_save_path}")
print(f"📁 Files available in /kaggle/working for download:")
print(f"  📄 classification_report.txt")
print(f"  📄 model_summary.txt")
print(f"  📄 model_metadata.json")
print(f"  📄 model_data.pkl")
print(f"  📁 bert_labor_classifier_final/ (complete model)")

# ============================================================================
# USAGE INSTRUCTIONS FOR KAGGLE
# ============================================================================
"""
KAGGLE USAGE INSTRUCTIONS:

1. Upload your Excel files (train_data_60.xlsx, val_data_20.xlsx, test_data_20.xlsx)
   as a Kaggle dataset

2. Update the file paths in CELL 2 (load_split_data function) to match your dataset name:
   - Replace "your-dataset" with your actual Kaggle dataset name

3. Copy each cell block into separate Kaggle notebook cells and run them in order

4. After training, download the following files from /kaggle/working:
   - bert_labor_classifier_final/ (complete trained model)
   - classification_report.txt (detailed performance report)
   - model_summary.txt (training summary)
   - model_metadata.json (model metadata)
   - model_data.pkl (model data for easy loading)

5. The model can be loaded later using:
   from transformers import AutoTokenizer, AutoModelForSequenceClassification
   model = AutoModelForSequenceClassification.from_pretrained("/path/to/bert_labor_classifier_final")
   tokenizer = AutoTokenizer.from_pretrained("/path/to/bert_labor_classifier_final")
"""
