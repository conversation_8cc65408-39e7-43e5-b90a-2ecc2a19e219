# ============================================================================
# KAGGLE BERT TRAINING WITH BUILT-IN UPSAMPLING
# Includes memory-efficient upsampling + fast BERT training
# ============================================================================

import pandas as pd
import numpy as np
import torch
import os
import warnings
import json
import random
from collections import Counter
from tqdm.auto import tqdm

warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"

from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from torch.optim import AdamW
from sklearn.metrics import accuracy_score, classification_report, f1_score

print("✅ Libraries imported")
print(f"🔥 CUDA available: {torch.cuda.is_available()}")

# CELL 1: Simple Dataset Class
class LaborDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=96):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

# CELL 2: Text Augmentation Functions
def augment_text(text):
    """Simple text augmentation for labor descriptions"""
    
    augmentations = [text]  # Include original
    
    # Common synonyms for labor descriptions
    replacements = {
        'REPLACE': ['CHANGE', 'SUBSTITUTE', 'SWAP'],
        'REPAIR': ['FIX', 'MEND', 'RESTORE'],
        'CHECK': ['INSPECT', 'EXAMINE', 'VERIFY'],
        'INSTALL': ['MOUNT', 'FIT', 'PLACE'],
        'REMOVE': ['TAKE OUT', 'EXTRACT'],
        'CLEAN': ['WASH', 'CLEAR'],
        'TEST': ['VERIFY', 'EXAMINE'],
        'ADJUST': ['MODIFY', 'TUNE']
    }
    
    # Apply word replacements
    words = text.split()
    for i, word in enumerate(words):
        if word in replacements:
            for replacement in replacements[word]:
                new_words = words.copy()
                new_words[i] = replacement
                augmentations.append(' '.join(new_words))
    
    # Add prefixes
    prefixes = ['CUSTOMER APPROVED ', 'COMPLETED ', 'PERFORMED ']
    for prefix in prefixes:
        if not text.startswith(prefix):
            augmentations.append(prefix + text)
    
    return augmentations

def upsample_data(df, text_col, label_col, target_samples_per_class=None):
    """Upsample minority classes using text augmentation"""
    
    print("📊 Analyzing class distribution...")
    
    # Count samples per class
    class_counts = df[label_col].value_counts()
    print(f"Original class distribution:")
    for label, count in class_counts.sort_index().items():
        print(f"  Class {label}: {count} samples")
    
    # Determine target count
    if target_samples_per_class is None:
        target_samples_per_class = int(class_counts.median() * 1.2)  # 20% above median
    
    print(f"\n🎯 Target samples per class: {target_samples_per_class}")
    
    # Upsample each class
    upsampled_data = []
    
    for class_label in df[label_col].unique():
        class_data = df[df[label_col] == class_label].copy()
        current_count = len(class_data)
        
        if current_count >= target_samples_per_class:
            # No upsampling needed
            upsampled_data.append(class_data)
            print(f"  Class {class_label}: {current_count} samples (no upsampling needed)")
        else:
            # Upsample this class
            needed = target_samples_per_class - current_count
            print(f"  Class {class_label}: {current_count} → {target_samples_per_class} (+{needed})")
            
            # Generate augmented samples
            original_texts = class_data[text_col].tolist()
            augmented_samples = []
            
            while len(augmented_samples) < needed:
                # Pick random original text
                base_text = random.choice(original_texts)
                
                # Generate augmentations
                augmented_texts = augment_text(base_text)
                
                # Add augmented samples
                for aug_text in augmented_texts[1:]:  # Skip original
                    if len(augmented_samples) >= needed:
                        break
                    
                    # Create new row
                    new_row = class_data.iloc[0].copy()
                    new_row[text_col] = aug_text
                    augmented_samples.append(new_row)
            
            # Combine original and augmented
            augmented_df = pd.DataFrame(augmented_samples[:needed])
            combined_class_data = pd.concat([class_data, augmented_df], ignore_index=True)
            upsampled_data.append(combined_class_data)
    
    # Combine all classes and shuffle
    final_df = pd.concat(upsampled_data, ignore_index=True)
    final_df = final_df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    print(f"\n✅ Upsampling complete!")
    print(f"📈 Original: {len(df)} → Upsampled: {len(final_df)} samples")
    
    return final_df

# CELL 3: Load and Upsample Data
print("📁 Loading and upsampling data...")

# Update these paths for your dataset
train_file = "/kaggle/input/your-dataset/train_data_60.xlsx"  # UPDATE THIS
val_file = "/kaggle/input/your-dataset/val_data_20.xlsx"      # UPDATE THIS
test_file = "/kaggle/input/your-dataset/test_data_20.xlsx"    # UPDATE THIS

# Load data
train_df = pd.read_excel(train_file)
val_df = pd.read_excel(val_file)
test_df = pd.read_excel(test_file)

print(f"✅ Loaded - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")

# Identify columns
text_col = 'labor_description_str'  # Update if different
label_col = 'category_num'          # Update if different

print(f"📝 Text column: {text_col}")
print(f"🏷️ Label column: {label_col}")

# Clean data
def clean_df(df):
    df = df.dropna(subset=[text_col, label_col])
    df[text_col] = df[text_col].astype(str)
    df = df[df[text_col].str.strip() != '']
    return df

train_df = clean_df(train_df)
val_df = clean_df(val_df)
test_df = clean_df(test_df)

# Upsample training data only
print("\n🚀 Upsampling training data...")
train_df_upsampled = upsample_data(train_df, text_col, label_col, target_samples_per_class=500)

# Create label mapping
all_classes = sorted(set(train_df_upsampled[label_col].unique()) | 
                    set(val_df[label_col].unique()) | 
                    set(test_df[label_col].unique()))

label_mapping = {old: new for new, old in enumerate(all_classes)}
reverse_mapping = {v: k for k, v in label_mapping.items()}

# Apply label mapping
train_df_upsampled['mapped_labels'] = train_df_upsampled[label_col].map(label_mapping)
val_df['mapped_labels'] = val_df[label_col].map(label_mapping)
test_df['mapped_labels'] = test_df[label_col].map(label_mapping)

num_classes = len(all_classes)
print(f"📊 Number of classes: {num_classes}")
print(f"📈 Final training samples: {len(train_df_upsampled)}")

# CELL 4: Fast Training Configuration
if torch.cuda.is_available():
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    BATCH_SIZE = 16 if gpu_memory >= 10 else 8
else:
    BATCH_SIZE = 4

EPOCHS = 3
LEARNING_RATE = 2e-5
MAX_LENGTH = 96

print(f"🚀 Training Configuration:")
print(f"  Batch Size: {BATCH_SIZE}")
print(f"  Epochs: {EPOCHS}")
print(f"  Max Length: {MAX_LENGTH}")

# CELL 5: Initialize Model and Data
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🖥️ Using device: {device}")

# Load model
model_name = 'bert-base-uncased'
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(
    model_name, 
    num_labels=num_classes
).to(device)

print("✅ Model loaded successfully")

# Prepare data
X_train = train_df_upsampled[text_col].tolist()
y_train = train_df_upsampled['mapped_labels'].tolist()
X_val = val_df[text_col].tolist()
y_val = val_df['mapped_labels'].tolist()

# Create datasets
train_dataset = LaborDataset(X_train, y_train, tokenizer, MAX_LENGTH)
val_dataset = LaborDataset(X_val, y_val, tokenizer, MAX_LENGTH)

train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)

optimizer = AdamW(model.parameters(), lr=LEARNING_RATE)

print(f"📚 Training batches: {len(train_loader)}")
print(f"🔍 Validation batches: {len(val_loader)}")

# CELL 6: Fast Training Loop
print("\n🚀 Starting Training with Upsampled Data")
print("=" * 50)

train_losses = []
val_accuracies = []

for epoch in range(EPOCHS):
    print(f"\n📖 Epoch {epoch + 1}/{EPOCHS}")
    
    # Clear cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Training
    model.train()
    total_loss = 0
    
    for batch_idx, batch in enumerate(tqdm(train_loader, desc="Training")):
        optimizer.zero_grad()
        
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)
        
        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels
        )
        
        loss = outputs.loss
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        
        # Memory management
        if batch_idx % 200 == 0 and batch_idx > 0 and torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    avg_loss = total_loss / len(train_loader)
    train_losses.append(avg_loss)
    print(f"📉 Average training loss: {avg_loss:.4f}")
    
    # Validation
    model.eval()
    val_predictions = []
    val_true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc="Validation", leave=False):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            
            val_predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            val_true_labels.extend(labels.cpu().numpy())
    
    val_accuracy = accuracy_score(val_true_labels, val_predictions)
    val_accuracies.append(val_accuracy)
    print(f"🎯 Validation Accuracy: {val_accuracy:.4f}")

print("\n✅ Training completed!")

# CELL 7: Final Evaluation and Save
print("\n🧪 Final Evaluation on Test Set")

# Test evaluation
X_test = test_df[text_col].tolist()
y_test = test_df['mapped_labels'].tolist()

test_dataset = LaborDataset(X_test, y_test, tokenizer, MAX_LENGTH)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

model.eval()
test_predictions = []
test_true_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Testing"):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)
        
        outputs = model(input_ids=input_ids, attention_mask=attention_mask)
        logits = outputs.logits
        
        test_predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
        test_true_labels.extend(labels.cpu().numpy())

# Calculate metrics
test_accuracy = accuracy_score(test_true_labels, test_predictions)
test_f1_macro = f1_score(test_true_labels, test_predictions, average='macro')
test_f1_weighted = f1_score(test_true_labels, test_predictions, average='weighted')

print(f"\n🎯 Final Test Results:")
print(f"📊 Accuracy: {test_accuracy:.4f}")
print(f"📊 F1 Score (Macro): {test_f1_macro:.4f}")
print(f"📊 F1 Score (Weighted): {test_f1_weighted:.4f}")

# Save model and results
model_save_path = "/kaggle/working/bert_labor_classifier_upsampled"
os.makedirs(model_save_path, exist_ok=True)

model.save_pretrained(model_save_path)
tokenizer.save_pretrained(model_save_path)

# Save metadata (fix JSON serialization issue)
metadata = {
    'model_type': 'BERT with Upsampling',
    'test_accuracy': float(test_accuracy),
    'test_f1_macro': float(test_f1_macro),
    'test_f1_weighted': float(test_f1_weighted),
    'train_losses': [float(x) for x in train_losses],
    'val_accuracies': [float(x) for x in val_accuracies],
    'label_mapping': {str(k): int(v) for k, v in label_mapping.items()},  # Convert keys to strings
    'reverse_mapping': {int(k): str(v) for k, v in reverse_mapping.items()},
    'upsampling_info': {
        'original_train_samples': len(train_df),
        'upsampled_train_samples': len(train_df_upsampled),
        'upsampling_ratio': len(train_df_upsampled) / len(train_df)
    }
}

with open("/kaggle/working/model_metadata_upsampled.json", 'w') as f:
    json.dump(metadata, f, indent=2)

print("✅ All results saved!")
print(f"📁 Model: {model_save_path}")
print(f"📄 Metadata: /kaggle/working/model_metadata_upsampled.json")

print(f"\n🎉 TRAINING WITH UPSAMPLING COMPLETED!")
print(f"📊 Final Accuracy: {test_accuracy:.4f}")
print(f"📈 Training samples increased from {len(train_df)} to {len(train_df_upsampled)}")
