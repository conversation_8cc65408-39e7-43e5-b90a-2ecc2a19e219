"""
Classification Report with Actual Category Names
Add this code to your BERT training script after model evaluation
"""

def create_classification_report_with_names(test_df, test_true_labels, test_predictions, 
                                          label_mapping, reverse_mapping, 
                                          test_accuracy, test_f1_macro, test_f1_weighted):
    """
    Create classification report with actual category names instead of numeric codes
    
    Parameters:
    - test_df: DataFrame containing test data with 'category_num' and 'labor_category_label' columns
    - test_true_labels: List of true labels (mapped to 0,1,2...)
    - test_predictions: List of predicted labels (mapped to 0,1,2...)
    - label_mapping: Dict mapping original category_num to mapped labels {original_num: mapped_label}
    - reverse_mapping: Dict mapping mapped labels to original category_num {mapped_label: original_num}
    - test_accuracy, test_f1_macro, test_f1_weighted: Performance metrics
    """
    
    from sklearn.metrics import classification_report
    
    print("🔍 Creating category name mapping...")
    
    # Get the mapping from your test data
    category_mapping = test_df[['category_num', 'labor_category_label']].drop_duplicates().sort_values('category_num')
    print(f"📋 Found {len(category_mapping)} unique categories")
    
    # Create mapping dictionary: {mapped_label: actual_category_name}
    # mapped_label is 0,1,2... and actual_category_name is the real category
    num_to_name_mapping = {}
    for _, row in category_mapping.iterrows():
        original_category_num = row['category_num']
        category_name = row['labor_category_label']
        # Find the mapped label (0,1,2...) that corresponds to this original category_num
        if original_category_num in label_mapping:
            mapped_label = label_mapping[original_category_num]
            num_to_name_mapping[mapped_label] = category_name
    
    print(f"\n📋 Category Mapping (Mapped Label → Actual Category Name):")
    for mapped_label, category_name in sorted(num_to_name_mapping.items()):
        print(f"  {mapped_label} → {category_name}")
    
    # Classification report with actual category names
    unique_labels = sorted(set(test_true_labels + test_predictions))
    target_names = [num_to_name_mapping.get(i, f"Unknown_{i}") for i in unique_labels]
    
    class_report = classification_report(
        test_true_labels,
        test_predictions,
        labels=unique_labels,
        target_names=target_names,
        digits=4
    )
    
    print("\n📊 Classification Report with Actual Category Names:")
    print(class_report)
    
    # Also create a detailed mapping table for reference
    print(f"\n📋 Detailed Category Mapping Reference:")
    print(f"{'Mapped Label':<15} {'Original Num':<15} {'Category Name':<40}")
    print("-" * 70)
    for mapped_label in sorted(num_to_name_mapping.keys()):
        original_num = reverse_mapping[mapped_label]
        category_name = num_to_name_mapping[mapped_label]
        print(f"{mapped_label:<15} {original_num:<15} {category_name:<40}")
    
    # Save classification report with category names to file
    report_path = "/kaggle/working/classification_report_with_names.txt"
    try:
        with open(report_path, 'w') as f:
            f.write("BERT Labor Description Classification Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Test Accuracy: {test_accuracy:.4f}\n")
            f.write(f"F1 Score (Macro): {test_f1_macro:.4f}\n")
            f.write(f"F1 Score (Weighted): {test_f1_weighted:.4f}\n\n")
            f.write("Classification Report with Actual Category Names:\n")
            f.write(class_report)
            f.write("\n\nCategory Mapping Reference:\n")
            f.write(f"{'Mapped Label':<15} {'Original Num':<15} {'Category Name':<40}\n")
            f.write("-" * 70 + "\n")
            for mapped_label in sorted(num_to_name_mapping.keys()):
                original_num = reverse_mapping[mapped_label]
                category_name = num_to_name_mapping[mapped_label]
                f.write(f"{mapped_label:<15} {original_num:<15} {category_name:<40}\n")
        
        print(f"\n✅ Classification report with category names saved to: {report_path}")
    except Exception as e:
        print(f"⚠️ Could not save to {report_path}: {e}")
        # Try saving to current directory
        local_path = "classification_report_with_names.txt"
        with open(local_path, 'w') as f:
            f.write("BERT Labor Description Classification Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Test Accuracy: {test_accuracy:.4f}\n")
            f.write(f"F1 Score (Macro): {test_f1_macro:.4f}\n")
            f.write(f"F1 Score (Weighted): {test_f1_weighted:.4f}\n\n")
            f.write("Classification Report with Actual Category Names:\n")
            f.write(class_report)
        print(f"✅ Classification report saved to: {local_path}")
    
    return class_report, num_to_name_mapping

# Example usage - Add this to your BERT training script after evaluation:
"""
# After you have calculated test_predictions and test_true_labels:

class_report_with_names, category_name_mapping = create_classification_report_with_names(
    test_df=test_df,
    test_true_labels=test_true_labels,
    test_predictions=test_predictions,
    label_mapping=label_mapping,
    reverse_mapping=reverse_mapping,
    test_accuracy=test_accuracy,
    test_f1_macro=test_f1_macro,
    test_f1_weighted=test_f1_weighted
)
"""

# ============================================================================
# STANDALONE VERSION - Run this if you have saved predictions
# ============================================================================

def load_and_create_report_standalone():
    """
    Standalone version - use this if you have already saved your model predictions
    """
    import pandas as pd
    import numpy as np
    import pickle
    
    # Load your test data
    test_file = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/test_data_20.xlsx"  # Update path
    test_df = pd.read_excel(test_file)
    
    print(f"✅ Loaded test data: {len(test_df)} samples")
    print(f"📋 Columns: {test_df.columns.tolist()}")
    
    # If you have saved predictions and metadata, load them
    try:
        # Try to load saved model metadata
        with open("/kaggle/working/model_metadata.json", 'r') as f:
            metadata = json.load(f)
        
        label_mapping = metadata['label_mapping']
        reverse_mapping = metadata['reverse_mapping']
        
        # Convert string keys back to integers if needed
        if isinstance(list(label_mapping.keys())[0], str):
            label_mapping = {int(k): v for k, v in label_mapping.items()}
        if isinstance(list(reverse_mapping.keys())[0], str):
            reverse_mapping = {int(k): v for k, v in reverse_mapping.items()}
        
        print("✅ Loaded label mappings from metadata")
        
    except FileNotFoundError:
        print("⚠️ No saved metadata found. Creating label mapping from test data...")
        
        # Create label mapping from test data
        unique_categories = sorted(test_df['category_num'].unique())
        label_mapping = {cat: i for i, cat in enumerate(unique_categories)}
        reverse_mapping = {i: cat for cat, i in label_mapping.items()}
    
    # If you have saved predictions, load them
    # Otherwise, you'll need to run model inference first
    
    print("\n📋 Available category mappings:")
    category_info = test_df[['category_num', 'labor_category_label']].drop_duplicates().sort_values('category_num')
    for _, row in category_info.iterrows():
        mapped_label = label_mapping.get(row['category_num'], 'Unknown')
        print(f"  {row['category_num']} → {mapped_label} → {row['labor_category_label']}")

if __name__ == "__main__":
    # Run standalone version
    load_and_create_report_standalone()

# ============================================================================
# INTEGRATION CODE FOR YOUR BERT TRAINING SCRIPT
# ============================================================================

"""
Add this code to your BERT training script right after the evaluation section:

# Replace your existing classification report code with this:

print("\\n🧪 Final Evaluation on Test Set")
print("=" * 40)

# ... your existing evaluation code ...

# Calculate metrics
test_accuracy = accuracy_score(test_true_labels, test_predictions)
test_f1_macro = f1_score(test_true_labels, test_predictions, average='macro')
test_f1_weighted = f1_score(test_true_labels, test_predictions, average='weighted')

print(f"\\n🎯 Final Test Results:")
print(f"📊 Accuracy: {test_accuracy:.4f}")
print(f"📊 F1 Score (Macro): {test_f1_macro:.4f}")
print(f"📊 F1 Score (Weighted): {test_f1_weighted:.4f}")

# NEW: Create classification report with actual category names
class_report_with_names, category_name_mapping = create_classification_report_with_names(
    test_df=test_df,
    test_true_labels=test_true_labels,
    test_predictions=test_predictions,
    label_mapping=label_mapping,
    reverse_mapping=reverse_mapping,
    test_accuracy=test_accuracy,
    test_f1_macro=test_f1_macro,
    test_f1_weighted=test_f1_weighted
)
"""
