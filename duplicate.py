def count_labor_descriptions_optimized(excel_file, output_txt):
    try:
        xls = pd.ExcelFile(excel_file)
        sheet_names = xls.sheet_names

        value_counts = {}

        for sheet in sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet, usecols=['labor_description_str'])
            counts = df['labor_description_str'].value_counts()

            for desc, count in counts.items():
                value_counts[desc] = value_counts.get(desc, 0) + count

        sorted_counts = dict(sorted(value_counts.items(), key=lambda item: item[1], reverse=True))
        duplicates = {k: v for k, v in sorted_counts.items() if v > 1}

        # Save to file
        with open(output_txt, 'w', encoding='utf-8') as f:
            f.write("📊 Count of each 'labor_description_str':\n")
            for desc, count in sorted_counts.items():
                f.write(f"{desc}: {count}\n")

            f.write("\n🔁 Duplicated Descriptions (count > 1):\n")
            for desc, count in duplicates.items():
                f.write(f"{desc}: {count}\n")

        print(f"✅ Saved results to: {output_txt}")

    except Exception as e:
        print(f"❌ Error: {e}")

# 🔍 Example usage
count_labor_descriptions_optimized(
    '/kaggle/input/merge-sheet/all_merge_new.xlsx',
    '/kaggle/working/description_counts.txt'
)
