"""
Kaggle-Friendly BERT Training for Labor Description Classification
Uses pre-split Excel files (train/val/test) in 60:20:20 ratio
Compatible with Kaggle notebooks and environments
"""

import pandas as pd
import numpy as np
import torch
import os
import warnings
import pickle
import json
from collections import Counter
from pathlib import Path

# Kaggle-friendly settings
warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Check if running in Kaggle
KAGGLE_ENV = os.path.exists('/kaggle')
if KAGGLE_ENV:
    print("🔍 Detected Kaggle environment")
    INPUT_PATH = "/kaggle/input"
    OUTPUT_PATH = "/kaggle/working"
else:
    print("🔍 Detected local environment")
    INPUT_PATH = "."
    OUTPUT_PATH = "."

# Import required libraries with error handling
try:
    from torch.utils.data import Dataset, DataLoader
    from transformers import (
        BertTokenizer, 
        BertForSequenceClassification, 
        get_linear_schedule_with_warmup,
        AutoTokenizer,
        AutoModelForSequenceClassification
    )
    from torch.optim import AdamW
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, f1_score
    from tqdm.auto import tqdm
    
    print("✅ All required libraries imported successfully")
    TRANSFORMERS_AVAILABLE = True
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Installing required packages...")
    
    if KAGGLE_ENV:
        print("Please ensure transformers and torch are available in your Kaggle environment")
    else:
        import subprocess
        subprocess.run(["pip", "install", "transformers", "torch", "scikit-learn", "tqdm"])
    
    TRANSFORMERS_AVAILABLE = False

class LaborDataset(Dataset):
    """Dataset class for labor descriptions"""
    
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def load_split_data():
    """Load pre-split train/validation/test data"""
    
    # Define file paths
    if KAGGLE_ENV:
        train_file = os.path.join(INPUT_PATH, "train_data_60.xlsx")
        val_file = os.path.join(INPUT_PATH, "val_data_20.xlsx")
        test_file = os.path.join(INPUT_PATH, "test_data_20.xlsx")
    else:
        train_file = "train_data_60.xlsx"
        val_file = "val_data_20.xlsx"
        test_file = "test_data_20.xlsx"
    
    print("📁 Loading pre-split data files...")
    
    # Load datasets
    try:
        train_df = pd.read_excel(train_file)
        val_df = pd.read_excel(val_file)
        test_df = pd.read_excel(test_file)
        
        print(f"✅ Train data: {train_df.shape[0]} samples")
        print(f"✅ Validation data: {val_df.shape[0]} samples")
        print(f"✅ Test data: {test_df.shape[0]} samples")
        
        return train_df, val_df, test_df
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        print("Available files:")
        for file in os.listdir(INPUT_PATH if KAGGLE_ENV else "."):
            if file.endswith('.xlsx'):
                print(f"  📄 {file}")
        raise

def prepare_data_for_training(train_df, val_df, test_df):
    """Prepare and clean data for training"""
    
    # Check required columns
    required_cols = ['labor_description_str', 'category_num']
    
    for name, df in [("Train", train_df), ("Validation", val_df), ("Test", test_df)]:
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"❌ Missing columns in {name}: {missing_cols}")
            print(f"Available columns: {df.columns.tolist()}")
            raise ValueError(f"Required columns not found in {name}: {missing_cols}")
    
    # Clean data
    print("🧹 Cleaning data...")
    
    def clean_dataframe(df):
        df = df.dropna(subset=required_cols)
        df['labor_description_str'] = df['labor_description_str'].astype(str)
        df = df[df['labor_description_str'].str.strip() != '']
        return df
    
    train_df = clean_dataframe(train_df)
    val_df = clean_dataframe(val_df)
    test_df = clean_dataframe(test_df)
    
    # Get unique classes from training data
    train_classes = set(train_df['category_num'].unique())
    val_classes = set(val_df['category_num'].unique())
    test_classes = set(test_df['category_num'].unique())
    
    all_classes = train_classes.union(val_classes).union(test_classes)
    
    print(f"📈 Total unique classes: {len(all_classes)}")
    print(f"🏷️ Classes: {sorted(all_classes)}")
    
    # Create label mapping (0-indexed)
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(sorted(all_classes))}
    reverse_mapping = {v: k for k, v in label_mapping.items()}
    
    # Apply label mapping
    train_df['mapped_labels'] = train_df['category_num'].map(label_mapping)
    val_df['mapped_labels'] = val_df['category_num'].map(label_mapping)
    test_df['mapped_labels'] = test_df['category_num'].map(label_mapping)
    
    # Get category mapping if available
    category_mapping = None
    if 'labor_category_label' in train_df.columns:
        category_mapping = train_df[['labor_category_label', 'category_num']].drop_duplicates().sort_values('category_num')
        print("\n🏷️ Category Mapping:")
        for _, row in category_mapping.iterrows():
            mapped_label = label_mapping.get(row['category_num'], 'Unknown')
            print(f"  {row['category_num']} → {mapped_label}: {row['labor_category_label']}")
    
    return train_df, val_df, test_df, len(all_classes), label_mapping, reverse_mapping, category_mapping

def train_bert_model_with_validation(train_df, val_df, num_classes, label_mapping,
                                   batch_size=8, epochs=3, learning_rate=2e-5, 
                                   model_name='bert-base-uncased'):
    """Train BERT model with separate validation set"""
    
    print(f"\n{'='*60}")
    print("🚀 BERT MODEL TRAINING WITH VALIDATION")
    print(f"{'='*60}")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Using device: {device}")
    
    if torch.cuda.is_available():
        print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Load model and tokenizer
    print(f"📥 Loading {model_name}...")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForSequenceClassification.from_pretrained(
            model_name, 
            num_labels=num_classes
        ).to(device)
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None, [], [], 0
    
    # Prepare data
    X_train = train_df['labor_description_str'].tolist()
    y_train = train_df['mapped_labels'].tolist()
    X_val = val_df['labor_description_str'].tolist()
    y_val = val_df['mapped_labels'].tolist()
    
    print(f"📚 Training samples: {len(X_train)}")
    print(f"🔍 Validation samples: {len(X_val)}")
    
    # Create datasets
    train_dataset = LaborDataset(X_train, y_train, tokenizer)
    val_dataset = LaborDataset(X_val, y_val, tokenizer)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # Setup optimizer
    optimizer = AdamW(model.parameters(), lr=learning_rate)
    total_steps = len(train_loader) * epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer, num_warmup_steps=0, num_training_steps=total_steps
    )
    
    # Training loop
    train_losses = []
    val_accuracies = []
    
    print(f"\n🎯 Starting training for {epochs} epoch(s)...")
    
    for epoch in range(epochs):
        print(f"\n📖 Epoch {epoch + 1}/{epochs}")
        
        # Training phase
        model.train()
        total_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch + 1}")
        
        for batch in progress_bar:
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            loss.backward()
            optimizer.step()
            scheduler.step()
            
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = total_loss / len(train_loader)
        train_losses.append(avg_loss)
        print(f"📉 Average training loss: {avg_loss:.4f}")
        
        # Validation phase
        model.eval()
        val_predictions = []
        val_true_labels = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validation"):
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                labels = batch['labels'].to(device)
                
                outputs = model(input_ids=input_ids, attention_mask=attention_mask)
                logits = outputs.logits
                
                val_predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
                val_true_labels.extend(labels.cpu().numpy())
        
        val_accuracy = accuracy_score(val_true_labels, val_predictions)
        val_accuracies.append(val_accuracy)
        print(f"🎯 Validation Accuracy: {val_accuracy:.4f}")
    
    return model, tokenizer, train_losses, val_accuracies, val_accuracy

def evaluate_model_on_test(model, tokenizer, test_df, label_mapping, reverse_mapping, category_mapping, device):
    """Evaluate model on test set and generate comprehensive report"""

    print(f"\n{'='*60}")
    print("🧪 FINAL MODEL EVALUATION ON TEST SET")
    print(f"{'='*60}")

    model.eval()

    # Prepare test data
    X_test = test_df['labor_description_str'].tolist()
    y_test = test_df['mapped_labels'].tolist()

    test_dataset = LaborDataset(X_test, y_test, tokenizer)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)

    # Get predictions
    predictions = []
    true_labels = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)

            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits

            predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            true_labels.extend(labels.cpu().numpy())

    # Calculate metrics
    accuracy = accuracy_score(true_labels, predictions)
    f1_macro = f1_score(true_labels, predictions, average='macro')
    f1_weighted = f1_score(true_labels, predictions, average='weighted')

    print(f"\n🎯 Test Results:")
    print(f"📊 Accuracy: {accuracy:.4f}")
    print(f"📊 F1 Score (Macro): {f1_macro:.4f}")
    print(f"📊 F1 Score (Weighted): {f1_weighted:.4f}")

    # Generate classification report
    target_names = None
    if category_mapping is not None:
        try:
            # Create target names using category labels
            target_names = []
            for i in range(len(label_mapping)):
                original_label = reverse_mapping[i]
                category_row = category_mapping[category_mapping['category_num'] == original_label]
                if not category_row.empty:
                    category_name = category_row['labor_category_label'].iloc[0]
                    target_names.append(f"Class_{original_label}_{category_name}")
                else:
                    target_names.append(f"Class_{original_label}")
        except:
            target_names = [f"Class_{reverse_mapping[i]}" for i in range(len(label_mapping))]
    else:
        target_names = [f"Class_{reverse_mapping[i]}" for i in range(len(label_mapping))]

    # Classification report
    class_report = classification_report(true_labels, predictions, target_names=target_names, output_dict=True)
    class_report_str = classification_report(true_labels, predictions, target_names=target_names)

    print("\n📊 Detailed Classification Report:")
    print(class_report_str)

    # Save classification report to Kaggle path
    report_path = os.path.join(OUTPUT_PATH, "classification_report.txt")
    with open(report_path, 'w') as f:
        f.write("BERT Labor Description Classification Report\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Test Accuracy: {accuracy:.4f}\n")
        f.write(f"F1 Score (Macro): {f1_macro:.4f}\n")
        f.write(f"F1 Score (Weighted): {f1_weighted:.4f}\n\n")
        f.write("Detailed Classification Report:\n")
        f.write(class_report_str)

    print(f"✅ Classification report saved to: {report_path}")

    return accuracy, f1_macro, f1_weighted, class_report

def save_model_kaggle_format(model, tokenizer, label_mapping, reverse_mapping, category_mapping,
                           train_losses, val_accuracies, test_accuracy, test_f1_macro, test_f1_weighted,
                           model_name="bert_labor_classifier_final"):
    """Save model with Kaggle-friendly paths and comprehensive metadata"""

    print(f"\n💾 Saving model in Kaggle format...")

    # Create output directory
    save_dir = os.path.join(OUTPUT_PATH, model_name)
    os.makedirs(save_dir, exist_ok=True)

    try:
        # Save model and tokenizer
        model.save_pretrained(save_dir)
        tokenizer.save_pretrained(save_dir)
        print(f"✅ Model saved to: {save_dir}")

        # Comprehensive metadata
        metadata = {
            'model_info': {
                'model_type': 'BERT',
                'model_name': model_name,
                'num_classes': len(label_mapping),
                'training_approach': 'Pre-split data (60:20:20)'
            },
            'performance_metrics': {
                'test_accuracy': test_accuracy,
                'test_f1_macro': test_f1_macro,
                'test_f1_weighted': test_f1_weighted,
                'final_validation_accuracy': val_accuracies[-1] if val_accuracies else None
            },
            'training_history': {
                'train_losses': train_losses,
                'validation_accuracies': val_accuracies
            },
            'label_mappings': {
                'original_to_model': label_mapping,
                'model_to_original': reverse_mapping
            }
        }

        # Save metadata as JSON
        with open(os.path.join(save_dir, 'model_metadata.json'), 'w') as f:
            json.dump(metadata, f, indent=2)

        # Save category mapping if available
        if category_mapping is not None:
            category_mapping.to_csv(os.path.join(save_dir, 'category_mapping.csv'), index=False)

        # Save comprehensive model data as pickle
        model_data = {
            'label_mapping': label_mapping,
            'reverse_mapping': reverse_mapping,
            'category_mapping': category_mapping,
            'test_accuracy': test_accuracy,
            'test_f1_macro': test_f1_macro,
            'test_f1_weighted': test_f1_weighted,
            'train_losses': train_losses,
            'validation_accuracies': val_accuracies
        }

        with open(os.path.join(save_dir, 'model_data.pkl'), 'wb') as f:
            pickle.dump(model_data, f)

        # Create summary file for Kaggle
        summary_path = os.path.join(OUTPUT_PATH, "model_summary.txt")
        with open(summary_path, 'w') as f:
            f.write("BERT Labor Description Classifier - Training Summary\n")
            f.write("=" * 55 + "\n\n")
            f.write(f"Model Type: BERT (bert-base-uncased)\n")
            f.write(f"Number of Classes: {len(label_mapping)}\n")
            f.write(f"Training Approach: Pre-split data (60:20:20)\n\n")
            f.write("Performance Metrics:\n")
            f.write(f"  Test Accuracy: {test_accuracy:.4f}\n")
            f.write(f"  Test F1 Score (Macro): {test_f1_macro:.4f}\n")
            f.write(f"  Test F1 Score (Weighted): {test_f1_weighted:.4f}\n\n")
            f.write(f"Model saved to: {save_dir}\n")
            f.write(f"Files available for download in Kaggle working directory\n")

        print(f"✅ Model summary saved to: {summary_path}")

        # List saved files
        print(f"\n📁 Saved files:")
        for file in os.listdir(save_dir):
            file_path = os.path.join(save_dir, file)
            size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"  📄 {file} ({size:.1f} MB)")

        return save_dir

    except Exception as e:
        print(f"❌ Error saving model: {e}")
        return None

def test_model_predictions(model, tokenizer, label_mapping, reverse_mapping, category_mapping, device):
    """Test model with sample predictions"""

    print(f"\n{'='*60}")
    print("🧪 TESTING SAMPLE PREDICTIONS")
    print(f"{'='*60}")

    model.eval()

    test_descriptions = [
        "CUSTOMER APPROVED FULL SYNTHETIC OIL CHANGE",
        "BATTERY TEST AND REPLACEMENT",
        "BRAKE PAD REPLACEMENT",
        "TIRE ROTATION AND BALANCE",
        "DIAGNOSIS OF ENGINE PROBLEM",
        "WIPER BLADE REPLACEMENT",
        "AIR FILTER REPLACEMENT",
        "TRANSMISSION FLUID CHANGE"
    ]

    for desc in test_descriptions:
        encoding = tokenizer(
            desc,
            truncation=True,
            padding='max_length',
            max_length=128,
            return_tensors='pt'
        ).to(device)

        with torch.no_grad():
            outputs = model(**encoding)
            logits = outputs.logits
            predicted_class = torch.argmax(logits, dim=-1).item()
            confidence = torch.softmax(logits, dim=-1).max().item()

        # Map back to original label
        original_label = reverse_mapping[predicted_class]

        # Get category name if available
        if category_mapping is not None:
            try:
                category_name = category_mapping[
                    category_mapping['category_num'] == original_label
                ]['labor_category_label'].iloc[0]
                category_display = f"{original_label} - {category_name}"
            except:
                category_display = str(original_label)
        else:
            category_display = str(original_label)

        print(f"🔧 Description: {desc}")
        print(f"🏷️ Predicted: {category_display}")
        print(f"📊 Confidence: {confidence:.4f}")
        print("-" * 50)

def main():
    """Main function for Kaggle-friendly BERT training with pre-split data"""

    print("🚀 KAGGLE-FRIENDLY BERT TRAINING (PRE-SPLIT DATA)")
    print("=" * 65)

    if not TRANSFORMERS_AVAILABLE:
        print("❌ Transformers not available. Please install required packages.")
        return

    try:
        # Load pre-split data
        train_df, val_df, test_df = load_split_data()

        # Prepare data
        train_df, val_df, test_df, num_classes, label_mapping, reverse_mapping, category_mapping = prepare_data_for_training(
            train_df, val_df, test_df
        )

        # Train model
        model, tokenizer, train_losses, val_accuracies, final_val_accuracy = train_bert_model_with_validation(
            train_df, val_df, num_classes, label_mapping,
            batch_size=8 if torch.cuda.is_available() else 4,
            epochs=3,
            learning_rate=2e-5
        )

        if model is not None:
            device = next(model.parameters()).device

            # Evaluate on test set
            test_accuracy, test_f1_macro, test_f1_weighted, class_report = evaluate_model_on_test(
                model, tokenizer, test_df, label_mapping, reverse_mapping, category_mapping, device
            )

            # Save model with comprehensive metadata
            save_dir = save_model_kaggle_format(
                model, tokenizer, label_mapping, reverse_mapping, category_mapping,
                train_losses, val_accuracies, test_accuracy, test_f1_macro, test_f1_weighted
            )

            # Test sample predictions
            test_model_predictions(model, tokenizer, label_mapping, reverse_mapping, category_mapping, device)

            print(f"\n🎉 TRAINING COMPLETED SUCCESSFULLY!")
            print(f"📊 Final Test Accuracy: {test_accuracy:.4f}")
            print(f"📊 Final Test F1 (Macro): {test_f1_macro:.4f}")
            print(f"📊 Final Test F1 (Weighted): {test_f1_weighted:.4f}")
            print(f"💾 Model saved to: {save_dir}")

            if KAGGLE_ENV:
                print(f"📁 Files available in /kaggle/working for download")
                print(f"📄 Classification report: /kaggle/working/classification_report.txt")
                print(f"📄 Model summary: /kaggle/working/model_summary.txt")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
