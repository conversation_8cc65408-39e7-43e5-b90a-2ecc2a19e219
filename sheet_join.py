# import pandas as pd
# import os
# import math

# def merge_all_excels_in_folder(input_folder, output_excel):
#     all_dfs = []

#     # Step 1: Read and collect all Excel files
#     for file in os.listdir(input_folder):
#         if file.endswith(".xlsx"):
#             file_path = os.path.join(input_folder, file)
#             df = pd.read_excel(file_path)
#             all_dfs.append(df)

#     merged_df = pd.concat(all_dfs, ignore_index=True)
#     total_rows = len(merged_df)

#     print(f"✅ Total merged rows: {total_rows}")

#     # Step 2: Split the DataFrame into 3 parts
#     part_size = math.ceil(total_rows / 3)
#     df_parts = [merged_df[i:i + part_size] for i in range(0, total_rows, part_size)]

#     # Step 3: Write each part into a different sheet
#     with pd.ExcelWriter(output_excel, engine='openpyxl') as writer:
#         for i, part in enumerate(df_parts):
#             sheet_name = f"Merged_Part_{i+1}"
#             part.to_excel(writer, sheet_name=sheet_name, index=False)
#             print(f"✅ Saved {len(part)} rows to sheet: {sheet_name}")

#     print(f"✅ Merged Excel with 3 sheets saved to: {output_excel}")

# # Example usage
# merge_all_excels_in_folder(
#     '/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/Archive_List',
#     '/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/all_merge_new1.xlsx'
# )

######################################
# import pandas as pd

# def summarize_labor_labels(excel_file):
#     # Load all sheets
#     xls = pd.ExcelFile(excel_file)
#     sheet_names = xls.sheet_names

#     # Read and combine all sheets
#     all_dfs = [pd.read_excel(xls, sheet_name=sheet) for sheet in sheet_names]
#     merged_df = pd.concat(all_dfs, ignore_index=True)

#     if 'labor_category_label' not in merged_df.columns:
#         print("⚠️ 'labor_category_label' column not found in any sheet.")
#         return

#     # Summary: unique labels
#     unique_labels = merged_df['labor_category_label'].unique()
#     print("🔹 Unique labels:", unique_labels)

#     # Summary: label counts
#     label_counts = merged_df['labor_category_label'].value_counts()
#     print("\n📊 Count of each unique label:")
#     print(label_counts)

# # Example usage
# summarize_labor_labels('/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/all_merge_new.xlsx')


# Unique labels: ['Wiper Blade/Arm/Insert' 'Accessory' 'Spark Plug' 'Diagnosis' 'Bulb'
#  'MISC' 'Alignment' 'Maint/Service' 'Brake - Pad/Rotor/L/S/D'
#  '*Not Classified (Exclude)' 'Tire' 'Belt - Drive'
#  'Mount and Balance (BMW)' 'Battery' 'Key Blank' 'Mechanical + Body'
#  'Body Collision' 'Engine Assembly' 'Key Battery' 'Hoses' 'Seat Belt'
#  'Transmission Assembly' 'PCV Valve' 'Fuse' 'Value Advantage'
#  'Fluid (All Kinds)' nan 'POWERTRAIN SOFTWARE UPDATE']

# 📊 Count of each unique label:
# labor_category_label
# Maint/Service                 399370
# MISC                          274854
# Mechanical + Body             221738
# Tire                          133309
# *Not Classified (Exclude)      94258
# Brake - Pad/Rotor/L/S/D        84859
# Diagnosis                      46947
# Battery                        36245
# Alignment                      30873
# Bulb                           28718
# Wiper Blade/Arm/Insert         27417
# Belt - Drive                   18564
# Accessory                      17683
# Spark Plug                     15327
# Key Blank                      14055
# Mount and Balance (BMW)        12631
# Hoses                           7615
# Body Collision                  7417
# Transmission Assembly           1756
# Engine Assembly                 1327
# PCV Valve                       1131
# Fuse                             457
# Seat Belt                        422
# Key Battery                      289
# Value Advantage                  189
# Fluid (All Kinds)                167
# POWERTRAIN SOFTWARE UPDATE         1

############################

# import pandas as pd

# def count_labor_descriptions(excel_file, output_txt):
#     # Load all sheets
#     xls = pd.ExcelFile(excel_file)
#     sheet_names = xls.sheet_names

#     # Read and combine all sheets
#     all_dfs = [pd.read_excel(xls, sheet_name=sheet) for sheet in sheet_names]
#     merged_df = pd.concat(all_dfs, ignore_index=True)

#     if 'labor_description_str' not in merged_df.columns:
#         print("⚠️ 'labor_description_str' column not found in any sheet.")
#         return

#     # Count all descriptions
#     description_counts = merged_df['labor_description_str'].value_counts()

#     # Filter only duplicates
#     duplicates_only = description_counts[description_counts > 1]

#     # Save both to a text file
#     with open(output_txt, 'w', encoding='utf-8') as f:
#         f.write("📊 Count of each 'labor_description_str':\n")
#         f.write(description_counts.to_string())
#         f.write("\n\n🔁 Duplicated Descriptions (count > 1):\n")
#         f.write(duplicates_only.to_string())

#     print(f"✅ Counts saved to: {output_txt}")

# # 🔍 Example usage
# count_labor_descriptions(
#     '/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/all_merge_new.xlsx',
#     '/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/description_counts.txt'
# )

import pandas as pd

def add_cleaned_unique_category_num(excel_file_path):
    xls = pd.ExcelFile(excel_file_path)
    sheet_names = xls.sheet_names

    # Combine all sheets
    combined_df = pd.concat(
        [pd.read_excel(xls, sheet_name=s) for s in sheet_names],
        ignore_index=True
    )

    if 'labor_category_label' not in combined_df.columns:
        print("❌ 'labor_category_label' column not found.")
        return

    # Clean labels: strip whitespace and drop NaNs
    labels = combined_df['labor_category_label'].fillna('').astype(str).str.strip()
    unique_labels = sorted(set(labels))  # now all unique and cleaned

    # Create proper mapping
    label_to_num = {label: i for i, label in enumerate(unique_labels)}

    # Print mapping
    print("\n📋 Cleaned Unique Category Label to category_num mapping:")
    for label, num in label_to_num.items():
        print(f"{label!r} → {num}")

    # Apply mapping to each sheet
    with pd.ExcelWriter(excel_file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        for sheet in sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet)
            if 'labor_category_label' in df.columns:
                df['category_num'] = df['labor_category_label'].fillna('').astype(str).str.strip().map(label_to_num)
                print(f"✅ Updated sheet: {sheet}")
            else:
                print(f"⚠️ 'labor_category_label' not found in sheet: {sheet}")
            df.to_excel(writer, sheet_name=sheet, index=False)

    print("\n✅ All sheets updated with cleaned unique category_num values.")



add_cleaned_unique_category_num('/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/all_merge_new.xlsx')