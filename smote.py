import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from imblearn.over_sampling import SMOTE
from sklearn.preprocessing import LabelEncoder
from openpyxl import Workbook
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
input_file = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train .xlsx"
sheet_name = "Sheet1"  # Change if different
df = pd.read_excel(input_file, sheet_name=sheet_name)

# Columns of interest
text_column = "labor_description"
label_column = "labor_category_label"

# TF-IDF Vectorization
vectorizer = TfidfVectorizer()
X_tfidf = vectorizer.fit_transform(df[text_column])

# Encode labels
label_encoder = LabelEncoder()
y_encoded = label_encoder.fit_transform(df[label_column])

# Desired number of rows per class
desired_samples = 800

# Count current samples per class
class_counts = pd.Series(y_encoded).value_counts()

# Determine which classes need more samples
sampling_strategy = {}
for label, count in class_counts.items():
    if count < desired_samples:
        sampling_strategy[label] = desired_samples

# Apply SMOTE only to undersampled classes
smote = SMOTE(sampling_strategy=sampling_strategy, random_state=42)
X_resampled, y_resampled = smote.fit_resample(X_tfidf, y_encoded)

# Decode labels back
y_labels = label_encoder.inverse_transform(y_resampled)

# Get upsampled text (SMOTE doesn't return the original text, so map from vector index)
# We'll use the nearest original TF-IDF vectors
# For simplicity, map nearest rows from original data


sim_matrix = cosine_similarity(X_resampled, X_tfidf)
nearest_indices = sim_matrix.argmax(axis=1)
resampled_texts = df.iloc[nearest_indices][text_column].values

# Create final DataFrame
upsampled_df = pd.DataFrame({
    text_column: resampled_texts,
    label_column: y_labels
})

# Save to Excel
output_file = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/upsampled_output.xlsx"
upsampled_df.to_excel(output_file, index=False)
print(f"Upsampled data saved to {output_file}")
