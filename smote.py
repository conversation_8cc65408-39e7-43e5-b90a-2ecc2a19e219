# import pandas as pd
# from sklearn.feature_extraction.text import TfidfVectorizer
# from imblearn.over_sampling import SMOTE
# from sklearn.preprocessing import LabelEncoder
# import numpy as np

# # 🔹 Load input Excel file
# input_path = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train.xlsx"  # ✅ Change to your actual Excel file path
# sheet_name = "Sheet1"  # ✅ Change if needed
# df = pd.read_excel(input_path, sheet_name=sheet_name)

# # 🔹 Define column names
# label_col = "labor_category_label"
# text_col = "labor_description"

# # 🔹 Target categories for augmentation
# target_categories = [
#     "Engine Assembly", "Fluid (All Kinds)", "Key Battery", "MISC",
#     "Seat Belt", "Transmission Assembly", "Value Advantage"
# ]

# # 🔹 Desired sample count per target category
# desired_samples = 500

# # 🔹 Prepare empty DataFrame for final results
# final_augmented_df = pd.DataFrame(columns=[label_col, text_col])

# # 🔹 TF-IDF Vectorizer
# vectorizer = TfidfVectorizer()

# # 🔁 Process each target category
# for category in target_categories:
#     print(f"\n🛠️ Processing category: '{category}'")
    
#     cat_df = df[df[label_col] == category]
#     current_count = len(cat_df)
#     print(f"   → Current sample count: {current_count}")
    
#     if current_count >= desired_samples:
#         print("   → Already sufficient. Adding original data.")
#         final_augmented_df = pd.concat([final_augmented_df, cat_df], ignore_index=True)
#         continue

#     # Prepare binary label data (1 = target category, 0 = other)
#     other_df = df[df[label_col] != category]
#     combined_df = pd.concat([cat_df, other_df], ignore_index=True)
#     X_text = combined_df[text_col]
#     y = (combined_df[label_col] == category).astype(int)

#     print("   → Vectorizing text...")
#     X_vec = vectorizer.fit_transform(X_text)

#     # SMOTE will upsample class 1 to `desired_samples`
#     print(f"   → Applying SMOTE for {desired_samples} samples in this category...")
#     smote = SMOTE(sampling_strategy={1: desired_samples}, random_state=42)
#     X_resampled, y_resampled = smote.fit_resample(X_vec, y)

#     # Extract synthetic samples only (new positive class rows beyond original count)
#     num_new_samples = np.sum(y_resampled) - current_count
#     print(f"   → Synthetic samples generated: {num_new_samples}")
#     synthetic_vecs = X_resampled[-num_new_samples:]
#     synthetic_words = vectorizer.inverse_transform(synthetic_vecs)
#     synthetic_texts = [" ".join(words) for words in synthetic_words]

#     # 🔹 Create DataFrame for synthetic data
#     synthetic_df = pd.DataFrame({
#         label_col: [category] * num_new_samples,
#         text_col: synthetic_texts
#     })

#     # 🔹 Combine with original and append to final dataset
#     combined_cat_df = pd.concat([cat_df, synthetic_df], ignore_index=True)
#     final_augmented_df = pd.concat([final_augmented_df, combined_cat_df], ignore_index=True)
#     print(f"   → Final count for '{category}': {len(combined_cat_df)}")

# # 🔹 Save final result to Excel
# output_excel = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/augmented_output_500_each_category.xlsx"
# final_augmented_df.to_excel(output_excel, index=False)
# print(f"\n✅ All done! Augmented dataset saved to: {output_excel}")




import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from imblearn.over_sampling import SMOTE
import numpy as np

# 🔹 Load input Excel file
input_path = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train.xlsx"
sheet_name = "Sheet1"
df = pd.read_excel(input_path, sheet_name=sheet_name)

# 🔹 Define column names
label_col = "labor_category_label"
text_col = "labor_description"
catnum_col = "category_num"

# 🔹 Target categories for augmentation
target_categories = [
    "Engine Assembly", "Fluid (All Kinds)", "Key Battery", "MISC",
    "Seat Belt", "Transmission Assembly", "Value Advantage"
]

# 🔹 Desired sample count per target category
desired_samples = 500

# 🔹 Prepare DataFrame to hold synthetic data
synthetic_all_df = pd.DataFrame(columns=[label_col, text_col, catnum_col])

# 🔹 TF-IDF Vectorizer
vectorizer = TfidfVectorizer()

# 🔁 Process each target category
for category in target_categories:
    print(f"\n🛠️ Processing category: '{category}'")
    
    cat_df = df[df[label_col] == category]
    current_count = len(cat_df)
    print(f"   → Current sample count: {current_count}")
    
    if current_count >= desired_samples:
        print("   → Already sufficient. Skipping augmentation.")
        continue

    # Prepare binary classification labels
    other_df = df[df[label_col] != category]
    combined_df = pd.concat([cat_df, other_df], ignore_index=True)
    X_text = combined_df[text_col]
    y = (combined_df[label_col] == category).astype(int)

    print("   → Vectorizing text...")
    X_vec = vectorizer.fit_transform(X_text)

    print(f"   → Applying SMOTE to reach {desired_samples} samples...")
    smote = SMOTE(sampling_strategy={1: desired_samples}, random_state=42)
    X_resampled, y_resampled = smote.fit_resample(X_vec, y)

    # Extract synthetic samples (the new rows at the end for positive class)
    num_new_samples = np.sum(y_resampled) - current_count
    print(f"   → Synthetic samples generated: {num_new_samples}")
    
    synthetic_vecs = X_resampled[-num_new_samples:]
    synthetic_words = vectorizer.inverse_transform(synthetic_vecs)
    
    # Preserve casing and add semicolon
    example_desc = cat_df.iloc[0][text_col]
    if example_desc.isupper():
        case_func = str.upper
    elif example_desc.istitle():
        case_func = str.title
    elif example_desc.islower():
        case_func = str.lower
    else:
        case_func = str.capitalize

    synthetic_texts = [
        case_func(" ".join(words)).strip() + ";" for words in synthetic_words
    ]

    # Get corresponding category number
    category_num = cat_df.iloc[0][catnum_col]

    # 🔹 Create DataFrame for synthetic samples
    synthetic_df = pd.DataFrame({
        label_col: [category] * num_new_samples,
        text_col: synthetic_texts,
        catnum_col: [category_num] * num_new_samples
    })

    synthetic_all_df = pd.concat([synthetic_all_df, synthetic_df], ignore_index=True)
    print(f"   → Total records for '{category}' after adding synthetic: {current_count + num_new_samples}")

# 🔹 Combine original + synthetic data
final_df = pd.concat([df, synthetic_all_df], ignore_index=True)
print(f"\n✅ Total final records: {len(final_df)}")

# 🔹 Save to Excel
output_excel = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/augmented_output_500_each_category.xlsx"
final_df.to_excel(output_excel, index=False)
print(f"✅ Augmented dataset saved to: {output_excel}")
