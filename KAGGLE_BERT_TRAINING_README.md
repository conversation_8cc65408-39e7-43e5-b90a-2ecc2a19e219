# Kaggle BERT Training for Labor Description Classification

This repository contains Kaggle-friendly code for training a BERT model on labor description data using pre-split Excel files in a 60:20:20 ratio (train:validation:test).

## 📁 Files Overview

### 1. `kaggle_bert_training_split_data.py`
- **Complete standalone script** for BERT training
- Works with pre-split Excel files
- Includes comprehensive evaluation and model saving
- Can be run locally or uploaded to Kaggle as a single script

### 2. `kaggle_notebook_cells_split_data.py`
- **Kaggle notebook format** - copy each cell block into separate Kaggle notebook cells
- Same functionality as the standalone script but organized for interactive notebook use
- Easier to debug and modify in Kaggle environment

### 3. `data_split_sheet.py`
- Your existing data splitting script
- Creates the 60:20:20 split from your original Excel file

## 🚀 Quick Start for Kaggle

### Option A: Using Notebook Cells (Recommended)

1. **Upload your data to Kaggle:**
   - Create a new Kaggle dataset
   - Upload your three Excel files:
     - `train_data_60.xlsx`
     - `val_data_20.xlsx` 
     - `test_data_20.xlsx`

2. **Create a new Kaggle notebook:**
   - Open `kaggle_notebook_cells_split_data.py`
   - Copy each cell block (marked with `# CELL X:`) into separate Kaggle notebook cells
   - Update the dataset name in CELL 2 (replace "your-dataset" with your actual dataset name)

3. **Run the cells in order:**
   - CELL 1: Install and import libraries
   - CELL 2: Define helper functions and update file paths
   - CELL 3: Load and prepare data
   - CELL 4: Define training function
   - CELL 5: Start training
   - CELL 6: Evaluate model
   - CELL 7: Save model and reports
   - CELL 8: Test sample predictions

### Option B: Using Complete Script

1. Upload `kaggle_bert_training_split_data.py` to your Kaggle notebook
2. Update the file paths in the `load_split_data()` function
3. Run the script

## 📊 What You'll Get

After training, the following files will be available in `/kaggle/working` for download:

### Model Files
- `bert_labor_classifier_final/` - Complete trained BERT model
  - `config.json` - Model configuration
  - `model.safetensors` - Model weights
  - `tokenizer_config.json` - Tokenizer configuration
  - `vocab.txt` - Vocabulary file

### Reports and Metadata
- `classification_report.txt` - Detailed performance metrics with F1 scores
- `model_summary.txt` - Training summary and key metrics
- `model_metadata.json` - Complete training metadata
- `model_data.pkl` - Python pickle file with all model data
- `category_mapping.csv` - Category label mappings (if available)

## 🎯 Key Features

### Performance Metrics
- **Accuracy**: Overall classification accuracy
- **F1 Score (Macro)**: Unweighted average F1 score across all classes
- **F1 Score (Weighted)**: Weighted average F1 score
- **Detailed Classification Report**: Per-class precision, recall, and F1 scores

### Kaggle-Friendly Design
- ✅ Automatic GPU detection and usage
- ✅ Memory-efficient training
- ✅ Progress bars with tqdm
- ✅ Comprehensive error handling
- ✅ All outputs saved to `/kaggle/working`
- ✅ Easy model downloading

### Training Features
- **Pre-split data support**: Uses your existing 60:20:20 split
- **Validation during training**: Monitors performance on validation set
- **Final test evaluation**: Comprehensive evaluation on test set
- **Sample predictions**: Tests model with example descriptions

## 🔧 Customization Options

### Training Parameters
```python
BATCH_SIZE = 8 if torch.cuda.is_available() else 4
EPOCHS = 3
LEARNING_RATE = 2e-5
```

### Model Selection
```python
model_name = 'bert-base-uncased'  # Can change to other BERT variants
```

### Data Columns
The code expects these columns in your Excel files:
- `labor_description_str` - Text descriptions
- `category_num` - Numeric category labels
- `labor_category_label` - Category names (optional)

## 📈 Expected Performance

Based on your data structure, you can expect:
- Training time: 15-30 minutes on Kaggle GPU
- Memory usage: ~2-4GB GPU memory
- Model size: ~400MB
- Accuracy: Typically 85-95% depending on data quality

## 🔄 Loading the Trained Model

After downloading from Kaggle:

```python
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import pickle

# Load model and tokenizer
model = AutoModelForSequenceClassification.from_pretrained("./bert_labor_classifier_final")
tokenizer = AutoTokenizer.from_pretrained("./bert_labor_classifier_final")

# Load metadata
with open("model_data.pkl", 'rb') as f:
    model_data = pickle.load(f)

label_mapping = model_data['label_mapping']
reverse_mapping = model_data['reverse_mapping']
```

## 🐛 Troubleshooting

### Common Issues:

1. **File not found errors:**
   - Check that your dataset name is correct in the file paths
   - Ensure all three Excel files are uploaded to your Kaggle dataset

2. **Memory errors:**
   - Reduce batch size to 4 or 2
   - Reduce max_length in tokenizer to 64

3. **CUDA errors:**
   - The code automatically falls back to CPU if GPU is not available
   - Reduce batch size if GPU memory is insufficient

### Getting Help:
- Check the console output for detailed error messages
- All functions include comprehensive error handling and logging
- The code will print available files if data loading fails

## 📝 Notes

- The code automatically detects Kaggle environment vs local environment
- All file paths are configured for Kaggle's directory structure
- Classification reports include F1 scores as requested
- Model saving follows Kaggle-friendly format for easy download
- Compatible with both CPU and GPU training

## 🎉 Success Indicators

You'll know the training was successful when you see:
- ✅ All libraries imported successfully
- ✅ Data loaded and cleaned
- ✅ Model training progress bars
- ✅ Validation accuracy improving
- ✅ Final test metrics displayed
- ✅ Model and reports saved
- 🎉 "TRAINING COMPLETED SUCCESSFULLY!" message

Happy training! 🚀
