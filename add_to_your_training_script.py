# ============================================================================
# ADD THIS CODE TO YOUR BERT TRAINING SCRIPT
# Replace your existing classification report section with this code
# ============================================================================

# After calculating test_accuracy, test_f1_macro, test_f1_weighted, add this:

print(f"\n🎯 Final Test Results:")
print(f"📊 Accuracy: {test_accuracy:.4f}")
print(f"📊 F1 Score (Macro): {test_f1_macro:.4f}")
print(f"📊 F1 Score (Weighted): {test_f1_weighted:.4f}")

# ============================================================================
# NEW: Classification Report with Actual Category Names
# ============================================================================

print("🔍 Creating classification report with actual category names...")

# Create mapping from mapped labels (0,1,2...) to actual category names
category_mapping = test_df[['category_num', 'labor_category_label']].drop_duplicates().sort_values('category_num')
print(f"📋 Found {len(category_mapping)} unique categories")

# Create the mapping dictionary
num_to_name_mapping = {}
for _, row in category_mapping.iterrows():
    original_category_num = row['category_num']
    category_name = row['labor_category_label']
    
    # Find the mapped label (0,1,2...) that corresponds to this original category_num
    if original_category_num in label_mapping:
        mapped_label = label_mapping[original_category_num]
        num_to_name_mapping[mapped_label] = category_name

print(f"\n📋 Category Mapping (Model Output → Actual Category Name):")
for mapped_label, category_name in sorted(num_to_name_mapping.items()):
    print(f"  {mapped_label} → {category_name}")

# Create classification report with actual category names
unique_labels = sorted(set(test_true_labels + test_predictions))
target_names = [num_to_name_mapping.get(i, f"Unknown_{i}") for i in unique_labels]

class_report = classification_report(
    test_true_labels,
    test_predictions,
    labels=unique_labels,
    target_names=target_names,
    digits=4
)

print("\n📊 Classification Report with Actual Category Names:")
print(class_report)

# Create a detailed mapping reference table
print(f"\n📋 Complete Category Mapping Reference:")
print(f"{'Model Output':<12} {'Original Num':<12} {'Category Name':<40}")
print("-" * 64)
for mapped_label in sorted(num_to_name_mapping.keys()):
    original_num = reverse_mapping[mapped_label]
    category_name = num_to_name_mapping[mapped_label]
    print(f"{mapped_label:<12} {original_num:<12} {category_name:<40}")

# Save the detailed report to file
report_path = "/kaggle/working/classification_report_with_category_names.txt"
try:
    with open(report_path, 'w') as f:
        f.write("BERT Labor Description Classification Report\n")
        f.write("=" * 55 + "\n\n")
        f.write(f"Test Accuracy: {test_accuracy:.4f}\n")
        f.write(f"F1 Score (Macro): {test_f1_macro:.4f}\n")
        f.write(f"F1 Score (Weighted): {test_f1_weighted:.4f}\n\n")
        f.write("Classification Report with Actual Category Names:\n")
        f.write(class_report)
        f.write("\n\nComplete Category Mapping Reference:\n")
        f.write(f"{'Model Output':<12} {'Original Num':<12} {'Category Name':<40}\n")
        f.write("-" * 64 + "\n")
        for mapped_label in sorted(num_to_name_mapping.keys()):
            original_num = reverse_mapping[mapped_label]
            category_name = num_to_name_mapping[mapped_label]
            f.write(f"{mapped_label:<12} {original_num:<12} {category_name:<40}\n")
    
    print(f"\n✅ Detailed classification report saved to: {report_path}")
    
except Exception as e:
    print(f"⚠️ Could not save to Kaggle path: {e}")
    # Try saving to current directory
    local_path = "classification_report_with_category_names.txt"
    with open(local_path, 'w') as f:
        f.write("BERT Labor Description Classification Report\n")
        f.write("=" * 55 + "\n\n")
        f.write(f"Test Accuracy: {test_accuracy:.4f}\n")
        f.write(f"F1 Score (Macro): {test_f1_macro:.4f}\n")
        f.write(f"F1 Score (Weighted): {test_f1_weighted:.4f}\n\n")
        f.write("Classification Report with Actual Category Names:\n")
        f.write(class_report)
    print(f"✅ Classification report saved to: {local_path}")

# ============================================================================
# EXAMPLE OUTPUT YOU'LL SEE:
# ============================================================================

"""
🔍 Creating classification report with actual category names...
📋 Found 25 unique categories

📋 Category Mapping (Model Output → Actual Category Name):
  0 → Air Filter
  1 → Battery
  2 → Brake - Pad Rotor L S D
  3 → Bulb
  4 → Diagnosis
  5 → Engine Assembly
  6 → Fluid (All Kinds)
  7 → Fuse
  8 → Hoses
  9 → Key Battery
  10 → Key Blank
  11 → MISC
  12 → Maint Service
  13 → Mechanical + Body
  14 → Mount and Balance (BMW)
  15 → PCV Valve
  16 → Seat Belt
  17 → Spark Plug
  18 → Tire
  19 → Transmission Assembly
  20 → Value Advantage
  21 → Wiper Blade Arm Insert

📊 Classification Report with Actual Category Names:
                           precision    recall  f1-score   support

              Air Filter       0.8500    0.8200    0.8347       150
                 Battery       0.9100    0.8800    0.8947       200
    Brake - Pad Rotor L S D    0.8700    0.9000    0.8848       300
                    Bulb       0.7800    0.7500    0.7647        80
               Diagnosis       0.9200    0.9100    0.9150       250
          Engine Assembly       0.8000    0.7800    0.7899       120
         Fluid (All Kinds)     0.8600    0.8400    0.8499       180
                    Fuse       0.7500    0.7200    0.7347        60
                   Hoses       0.8100    0.7900    0.7999       110
             Key Battery       0.9000    0.8700    0.8847        90
               Key Blank       0.8300    0.8100    0.8199        70
                    MISC       0.7200    0.6900    0.7047       100
            Maint Service       0.9500    0.9300    0.9399       500
        Mechanical + Body       0.9100    0.9200    0.9150       400
   Mount and Balance (BMW)     0.8400    0.8200    0.8299        85
              PCV Valve       0.7900    0.7600    0.7747        65
              Seat Belt       0.8200    0.8000    0.8099        75
             Spark Plug       0.8800    0.8600    0.8699       130
                   Tire       0.9300    0.9400    0.9350       350
     Transmission Assembly     0.8500    0.8300    0.8399       140
         Value Advantage       0.7700    0.7400    0.7547        95
   Wiper Blade Arm Insert     0.8000    0.7800    0.7899        88

                accuracy                           0.8650      3688
               macro avg       0.8350    0.8200    0.8274      3688
            weighted avg       0.8640    0.8650    0.8645      3688
"""
