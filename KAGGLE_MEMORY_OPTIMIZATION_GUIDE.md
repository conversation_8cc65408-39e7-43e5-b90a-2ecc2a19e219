# Kaggle GPU Memory Optimization Guide for BERT Training

## 🚀 Memory Management Features Added

Your updated BERT training code now includes comprehensive memory management to prevent GPU memory issues in Kaggle:

### ✅ **Automatic Memory Clearing**
- **Every 100 batches**: `torch.cuda.empty_cache()` during training
- **Every 50 batches**: Cache clearing during validation  
- **After each epoch**: Complete cache clearing
- **After checkpoints**: Immediate cache clearing

### ✅ **Smart Checkpointing**
- **Every 1000 batches**: Model + tokenizer checkpoint
- **After each epoch**: Complete epoch checkpoint with training progress
- **Automatic cleanup**: Cache clearing after each checkpoint save

### ✅ **Memory Monitoring**
- **Real-time memory display**: Shows GPU memory usage in progress bars
- **Memory status logging**: Detailed memory allocation tracking
- **Adaptive batch sizing**: Automatically adjusts based on available GPU memory

## 🔧 Key Memory Optimizations

### 1. **Adaptive Batch Size Configuration**
```python
# Automatically adjusts based on GPU memory
if gpu_memory >= 15:  # High-end GPU (T4x, P100)
    BATCH_SIZE = 16
elif gpu_memory >= 10:  # Mid-range GPU (T4)
    BATCH_SIZE = 8
else:  # Lower memory GPU
    BATCH_SIZE = 4
```

### 2. **Periodic Cache Clearing**
```python
# Clear cache every 100 training batches
if batch_idx % 100 == 0 and batch_idx > 0 and torch.cuda.is_available():
    torch.cuda.empty_cache()

# Clear cache every 50 validation batches  
if batch_idx % 50 == 0 and batch_idx > 0 and torch.cuda.is_available():
    torch.cuda.empty_cache()
```

### 3. **Checkpoint Strategy**
```python
# Checkpoint every 1000 batches to prevent progress loss
if batch_idx % 1000 == 0 and batch_idx > 0:
    checkpoint_path = f"/kaggle/working/checkpoint_epoch{epoch+1}_batch{batch_idx}"
    model.save_pretrained(checkpoint_path)
    tokenizer.save_pretrained(checkpoint_path)
```

### 4. **Memory Monitoring Functions**
```python
def monitor_gpu_memory():
    """Real-time GPU memory monitoring"""
    allocated = torch.cuda.memory_allocated() / 1024**3
    reserved = torch.cuda.memory_reserved() / 1024**3
    total = torch.cuda.get_device_properties(0).total_memory / 1024**3
    return allocated, reserved, total

def clear_gpu_cache_aggressive():
    """Aggressive cache clearing with garbage collection"""
    torch.cuda.empty_cache()
    torch.cuda.ipc_collect()
    gc.collect()
```

## 📊 Kaggle GPU Memory Limits

### **Kaggle GPU Types and Memory:**
- **Tesla T4**: ~15GB VRAM (most common)
- **Tesla P100**: ~16GB VRAM  
- **Tesla K80**: ~12GB VRAM (older)

### **Recommended Settings by GPU:**

#### **Tesla T4 (15GB)**
```python
BATCH_SIZE = 8-16
MAX_LENGTH = 128
EPOCHS = 3-5
```

#### **Tesla P100 (16GB)**
```python
BATCH_SIZE = 16-32
MAX_LENGTH = 128-256
EPOCHS = 3-5
```

#### **Tesla K80 (12GB)**
```python
BATCH_SIZE = 4-8
MAX_LENGTH = 64-128
EPOCHS = 2-3
```

## 🛠️ Troubleshooting Memory Issues

### **If you get "CUDA out of memory" errors:**

#### **1. Reduce Batch Size**
```python
BATCH_SIZE = 4  # or even 2
```

#### **2. Reduce Sequence Length**
```python
MAX_LENGTH = 64  # instead of 128
```

#### **3. Use Gradient Accumulation**
```python
GRADIENT_ACCUMULATION_STEPS = 4  # Effective batch size = BATCH_SIZE * 4
```

#### **4. Enable Mixed Precision (Advanced)**
```python
from torch.cuda.amp import autocast, GradScaler

scaler = GradScaler()

# In training loop:
with autocast():
    outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
    loss = outputs.loss

scaler.scale(loss).backward()
scaler.step(optimizer)
scaler.update()
```

### **Emergency Memory Recovery**
```python
# Add this cell if you run into memory issues:
import gc
torch.cuda.empty_cache()
torch.cuda.ipc_collect()
gc.collect()

# Check memory status
if torch.cuda.is_available():
    print(f"Memory allocated: {torch.cuda.memory_allocated()/1024**3:.1f}GB")
    print(f"Memory reserved: {torch.cuda.memory_reserved()/1024**3:.1f}GB")
```

## 📈 Performance vs Memory Trade-offs

### **High Performance (More Memory)**
```python
BATCH_SIZE = 16
MAX_LENGTH = 256
GRADIENT_ACCUMULATION_STEPS = 1
```

### **Balanced (Recommended)**
```python
BATCH_SIZE = 8
MAX_LENGTH = 128
GRADIENT_ACCUMULATION_STEPS = 2
```

### **Memory Conservative**
```python
BATCH_SIZE = 4
MAX_LENGTH = 64
GRADIENT_ACCUMULATION_STEPS = 4
```

## 🔄 Checkpoint Recovery

If training is interrupted, you can resume from checkpoints:

```python
# Load from checkpoint
checkpoint_path = "/kaggle/working/checkpoint_epoch2_batch1000"
model = AutoModelForSequenceClassification.from_pretrained(checkpoint_path)
tokenizer = AutoTokenizer.from_pretrained(checkpoint_path)

# Load training progress
with open(f"{checkpoint_path}/training_progress.json", 'r') as f:
    progress = json.load(f)
    
print(f"Resuming from epoch {progress['epoch']}")
print(f"Previous losses: {progress['train_losses']}")
print(f"Previous validation accuracies: {progress['val_accuracies']}")
```

## 🎯 Best Practices for Kaggle

### **1. Start Conservative**
- Begin with small batch sizes and increase gradually
- Monitor memory usage in the first few batches
- Use the memory monitoring functions

### **2. Use Checkpoints Strategically**
- Save checkpoints every 1000 batches (not too frequent)
- Keep only the last 2-3 checkpoints to save disk space
- Always save after each epoch

### **3. Monitor Progress**
- Watch the memory usage in progress bars
- Look for memory leaks (constantly increasing usage)
- Clear cache if memory usage grows unexpectedly

### **4. Plan for Interruptions**
- Kaggle sessions can timeout after 12 hours
- Use epoch checkpoints to resume training
- Save intermediate results frequently

## 🚨 Warning Signs

Watch out for these memory issues:

### **Memory Leak Indicators:**
- Memory usage constantly increasing
- Training slowing down over time
- "CUDA out of memory" after several epochs

### **Solutions:**
- Increase cache clearing frequency
- Reduce batch size
- Check for variables not being released

## 📁 Checkpoint File Structure

Your checkpoints will be saved as:
```
/kaggle/working/
├── checkpoint_epoch1_batch1000/
│   ├── config.json
│   ├── model.safetensors
│   ├── tokenizer_config.json
│   ├── vocab.txt
│   └── training_progress.json
├── checkpoint_epoch1_complete/
│   └── [same structure]
└── checkpoint_epoch2_batch1000/
    └── [same structure]
```

This comprehensive memory management should prevent GPU memory issues and allow your BERT training to complete successfully on Kaggle! 🚀
