"""
Memory-Efficient Data Upsampling for BERT Training
Uses text-based augmentation instead of SMOTE to avoid memory issues
"""

import pandas as pd
import numpy as np
import random
from collections import Counter
import re
import os

def load_training_data():
    """Load your training data"""
    # Update this path to your actual training file
    input_file = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train_data_60.xlsx"
    
    try:
        df = pd.read_excel(input_file)
        print(f"✅ Loaded {len(df)} training samples")
        return df
    except FileNotFoundError:
        print(f"❌ File not found: {input_file}")
        print("Available files:")
        for file in os.listdir("."):
            if file.endswith('.xlsx'):
                print(f"  📄 {file}")
        return None

def analyze_class_distribution(df, label_column):
    """Analyze class distribution and identify classes that need upsampling"""
    
    class_counts = df[label_column].value_counts().sort_index()
    
    print(f"\n📊 Class Distribution Analysis:")
    print(f"{'Class':<15} {'Count':<10} {'Percentage':<12}")
    print("-" * 40)
    
    total_samples = len(df)
    for class_label, count in class_counts.items():
        percentage = (count / total_samples) * 100
        print(f"{str(class_label):<15} {count:<10} {percentage:<12.2f}%")
    
    # Identify classes that need upsampling
    median_count = class_counts.median()
    target_count = int(median_count * 1.5)  # Target 1.5x median
    
    classes_to_upsample = {}
    for class_label, count in class_counts.items():
        if count < target_count:
            needed = target_count - count
            classes_to_upsample[class_label] = needed
    
    print(f"\n🎯 Upsampling Strategy:")
    print(f"Target samples per class: {target_count}")
    print(f"Classes needing upsampling: {len(classes_to_upsample)}")
    
    for class_label, needed in classes_to_upsample.items():
        current = class_counts[class_label]
        print(f"  Class {class_label}: {current} → {target_count} (+{needed})")
    
    return classes_to_upsample, target_count

def text_augmentation_techniques(text):
    """Apply various text augmentation techniques"""
    
    augmented_texts = []
    
    # Original text
    augmented_texts.append(text)
    
    # 1. Synonym replacement (simple word variations)
    synonym_map = {
        'replace': ['change', 'substitute', 'swap'],
        'repair': ['fix', 'mend', 'restore'],
        'check': ['inspect', 'examine', 'verify'],
        'install': ['mount', 'fit', 'place'],
        'remove': ['take out', 'extract', 'detach'],
        'clean': ['wash', 'sanitize', 'clear'],
        'test': ['check', 'verify', 'examine'],
        'adjust': ['modify', 'tune', 'calibrate'],
        'service': ['maintain', 'repair', 'overhaul']
    }
    
    # Apply synonym replacement
    words = text.lower().split()
    for i, word in enumerate(words):
        if word in synonym_map:
            for synonym in synonym_map[word]:
                new_words = words.copy()
                new_words[i] = synonym
                augmented_texts.append(' '.join(new_words).upper())
    
    # 2. Add common prefixes/suffixes
    prefixes = ['CUSTOMER APPROVED ', 'COMPLETED ', 'PERFORMED ']
    suffixes = [' AS REQUESTED', ' PER CUSTOMER', ' SUCCESSFULLY']
    
    for prefix in prefixes:
        if not text.startswith(prefix):
            augmented_texts.append(prefix + text)
    
    for suffix in suffixes:
        if not text.endswith(suffix):
            augmented_texts.append(text + suffix)
    
    # 3. Rearrange words (for multi-word descriptions)
    words = text.split()
    if len(words) > 2:
        # Swap first two words if possible
        if len(words) >= 2:
            swapped = [words[1], words[0]] + words[2:]
            augmented_texts.append(' '.join(swapped))
    
    return augmented_texts

def upsample_class_data(df, class_label, text_column, label_column, target_count):
    """Upsample data for a specific class using text augmentation"""
    
    class_data = df[df[label_column] == class_label].copy()
    current_count = len(class_data)
    needed = target_count - current_count
    
    if needed <= 0:
        return class_data
    
    print(f"  📈 Upsampling class {class_label}: {current_count} → {target_count}")
    
    # Get all original texts for this class
    original_texts = class_data[text_column].tolist()
    
    # Generate augmented samples
    augmented_samples = []
    
    while len(augmented_samples) < needed:
        # Randomly select an original text
        base_text = random.choice(original_texts)
        
        # Generate augmentations
        augmented_texts = text_augmentation_techniques(base_text)
        
        # Add augmented samples (excluding the original)
        for aug_text in augmented_texts[1:]:  # Skip original
            if len(augmented_samples) >= needed:
                break
            
            # Create new sample
            new_sample = class_data.iloc[0].copy()  # Copy structure
            new_sample[text_column] = aug_text
            new_sample[label_column] = class_label
            
            augmented_samples.append(new_sample)
    
    # Create DataFrame from augmented samples
    augmented_df = pd.DataFrame(augmented_samples[:needed])
    
    # Combine original and augmented data
    upsampled_class_data = pd.concat([class_data, augmented_df], ignore_index=True)
    
    return upsampled_class_data

def create_upsampled_dataset(df, text_column, label_column):
    """Create upsampled dataset using text augmentation"""
    
    print("🚀 Starting Memory-Efficient Upsampling...")
    
    # Analyze class distribution
    classes_to_upsample, target_count = analyze_class_distribution(df, label_column)
    
    if not classes_to_upsample:
        print("✅ No upsampling needed - classes are already balanced!")
        return df
    
    # Upsample each class that needs it
    upsampled_classes = []
    
    for class_label in df[label_column].unique():
        if class_label in classes_to_upsample:
            # Upsample this class
            upsampled_class_data = upsample_class_data(
                df, class_label, text_column, label_column, target_count
            )
        else:
            # Keep original data for this class
            upsampled_class_data = df[df[label_column] == class_label].copy()
        
        upsampled_classes.append(upsampled_class_data)
    
    # Combine all classes
    final_df = pd.concat(upsampled_classes, ignore_index=True)
    
    # Shuffle the data
    final_df = final_df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    print(f"\n✅ Upsampling Complete!")
    print(f"📊 Original dataset: {len(df)} samples")
    print(f"📊 Upsampled dataset: {len(final_df)} samples")
    print(f"📈 Increase: {len(final_df) - len(df)} samples ({((len(final_df) - len(df)) / len(df) * 100):.1f}%)")
    
    return final_df

def save_upsampled_data(df, output_path):
    """Save upsampled data to Excel"""
    
    try:
        df.to_excel(output_path, index=False, engine='openpyxl')
        print(f"💾 Upsampled data saved to: {output_path}")
        
        # Show final class distribution
        print(f"\n📊 Final Class Distribution:")
        final_counts = df.iloc[:, -1].value_counts().sort_index()  # Assuming last column is label
        for class_label, count in final_counts.items():
            print(f"  Class {class_label}: {count} samples")
            
    except Exception as e:
        print(f"❌ Error saving file: {e}")

def main():
    """Main function for upsampling"""
    
    print("🚀 Memory-Efficient Data Upsampling for BERT Training")
    print("=" * 60)
    
    # Load data
    df = load_training_data()
    if df is None:
        return
    
    # Identify text and label columns
    print(f"\n📋 Available columns: {df.columns.tolist()}")
    
    # Try to identify columns automatically
    text_column = None
    label_column = None
    
    for col in df.columns:
        if 'description' in col.lower() or 'text' in col.lower():
            text_column = col
        elif 'category' in col.lower() or 'label' in col.lower():
            label_column = col
    
    if text_column is None:
        text_column = input("Enter the text column name: ")
    if label_column is None:
        label_column = input("Enter the label column name: ")
    
    print(f"📝 Using text column: {text_column}")
    print(f"🏷️ Using label column: {label_column}")
    
    # Create upsampled dataset
    upsampled_df = create_upsampled_dataset(df, text_column, label_column)
    
    # Save upsampled data
    output_path = "/home/<USER>/Pictures/athu/ARMATUS/BERT_MODEL 1(1)/demo1/train_data_upsampled.xlsx"
    save_upsampled_data(upsampled_df, output_path)
    
    print(f"\n🎉 Upsampling completed successfully!")
    print(f"📁 Use the upsampled file for BERT training: {output_path}")

if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    main()
