# ============================================================================
# LIGHTWEIGHT KAGGLE BERT TRAINING (NO HEAVY CHECKPOINTING)
# Use this if the main training gets stuck at checkpoints
# ============================================================================

# CELL 1: Quick Setup
import pandas as pd
import numpy as np
import torch
import os
import warnings
import json
from tqdm.auto import tqdm

warnings.filterwarnings('ignore')
os.environ["TOKENIZERS_PARALLELISM"] = "false"

from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from torch.optim import AdamW
from sklearn.metrics import accuracy_score, classification_report, f1_score

print("✅ Libraries imported")
print(f"🔥 CUDA available: {torch.cuda.is_available()}")

# CELL 2: Simple Dataset Class
class SimpleLaborDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_length=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

print("✅ Dataset class defined")

# CELL 3: Load Data (Update paths for your dataset)
# Update these paths to match your Kaggle dataset
train_file = "/kaggle/input/your-dataset/train_data_60.xlsx"  # UPDATE THIS
val_file = "/kaggle/input/your-dataset/val_data_20.xlsx"      # UPDATE THIS
test_file = "/kaggle/input/your-dataset/test_data_20.xlsx"    # UPDATE THIS

print("📁 Loading data...")
train_df = pd.read_excel(train_file)
val_df = pd.read_excel(val_file)
test_df = pd.read_excel(test_file)

print(f"✅ Train: {len(train_df)} samples")
print(f"✅ Val: {len(val_df)} samples") 
print(f"✅ Test: {len(test_df)} samples")

# Clean data
required_cols = ['labor_description_str', 'category_num']

def clean_df(df):
    df = df.dropna(subset=required_cols)
    df['labor_description_str'] = df['labor_description_str'].astype(str)
    df = df[df['labor_description_str'].str.strip() != '']
    return df

train_df = clean_df(train_df)
val_df = clean_df(val_df)
test_df = clean_df(test_df)

# Create label mapping
all_classes = sorted(set(train_df['category_num'].unique()) | 
                    set(val_df['category_num'].unique()) | 
                    set(test_df['category_num'].unique()))

label_mapping = {old: new for new, old in enumerate(all_classes)}
reverse_mapping = {v: k for k, v in label_mapping.items()}

train_df['mapped_labels'] = train_df['category_num'].map(label_mapping)
val_df['mapped_labels'] = val_df['category_num'].map(label_mapping)
test_df['mapped_labels'] = test_df['category_num'].map(label_mapping)

num_classes = len(all_classes)
print(f"📊 Number of classes: {num_classes}")

# CELL 4: Simple Training Configuration
# Conservative settings to avoid memory issues
BATCH_SIZE = 4  # Small batch size to prevent memory issues
EPOCHS = 2      # Fewer epochs to complete faster
LEARNING_RATE = 2e-5
MAX_LENGTH = 128

print(f"🚀 Training Configuration:")
print(f"  Batch Size: {BATCH_SIZE}")
print(f"  Epochs: {EPOCHS}")
print(f"  Learning Rate: {LEARNING_RATE}")

# CELL 5: Initialize Model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🖥️ Using device: {device}")

# Load model
model_name = 'bert-base-uncased'
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(
    model_name, 
    num_labels=num_classes
).to(device)

print("✅ Model loaded successfully")

# Prepare data
X_train = train_df['labor_description_str'].tolist()
y_train = train_df['mapped_labels'].tolist()
X_val = val_df['labor_description_str'].tolist()
y_val = val_df['mapped_labels'].tolist()

# Create datasets
train_dataset = SimpleLaborDataset(X_train, y_train, tokenizer, MAX_LENGTH)
val_dataset = SimpleLaborDataset(X_val, y_val, tokenizer, MAX_LENGTH)

train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)

# Setup optimizer
optimizer = AdamW(model.parameters(), lr=LEARNING_RATE)

print(f"📚 Training batches: {len(train_loader)}")
print(f"🔍 Validation batches: {len(val_loader)}")

# CELL 6: Lightweight Training Loop (NO CHECKPOINTING)
print("\n🚀 Starting Lightweight Training (No Checkpointing)")
print("=" * 60)

train_losses = []
val_accuracies = []

for epoch in range(EPOCHS):
    print(f"\n📖 Epoch {epoch + 1}/{EPOCHS}")
    
    # Clear cache at start of epoch
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Training
    model.train()
    total_loss = 0
    
    train_progress = tqdm(train_loader, desc=f"Training Epoch {epoch + 1}")
    
    for batch_idx, batch in enumerate(train_progress):
        optimizer.zero_grad()
        
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)
        
        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels
        )
        
        loss = outputs.loss
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        
        # Simple progress update (no heavy operations)
        if batch_idx % 50 == 0:
            current_avg = total_loss / (batch_idx + 1)
            train_progress.set_postfix({'loss': f'{current_avg:.4f}'})
        
        # Light memory management
        if batch_idx % 200 == 0 and batch_idx > 0 and torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    avg_loss = total_loss / len(train_loader)
    train_losses.append(avg_loss)
    print(f"📉 Average training loss: {avg_loss:.4f}")
    
    # Validation
    model.eval()
    val_predictions = []
    val_true_labels = []
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc="Validation"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            logits = outputs.logits
            
            val_predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
            val_true_labels.extend(labels.cpu().numpy())
    
    val_accuracy = accuracy_score(val_true_labels, val_predictions)
    val_accuracies.append(val_accuracy)
    print(f"🎯 Validation Accuracy: {val_accuracy:.4f}")
    
    # Clear cache after epoch
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

print("\n✅ Training completed!")

# CELL 7: Final Evaluation
print("\n🧪 Final Evaluation on Test Set")
print("=" * 40)

# Prepare test data
X_test = test_df['labor_description_str'].tolist()
y_test = test_df['mapped_labels'].tolist()

test_dataset = SimpleLaborDataset(X_test, y_test, tokenizer, MAX_LENGTH)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

# Test evaluation
model.eval()
test_predictions = []
test_true_labels = []

with torch.no_grad():
    for batch in tqdm(test_loader, desc="Testing"):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        labels = batch['labels'].to(device)
        
        outputs = model(input_ids=input_ids, attention_mask=attention_mask)
        logits = outputs.logits
        
        test_predictions.extend(torch.argmax(logits, dim=-1).cpu().numpy())
        test_true_labels.extend(labels.cpu().numpy())

# Calculate metrics
test_accuracy = accuracy_score(test_true_labels, test_predictions)
test_f1_macro = f1_score(test_true_labels, test_predictions, average='macro')
test_f1_weighted = f1_score(test_true_labels, test_predictions, average='weighted')

print(f"\n🎯 Final Test Results:")
print(f"📊 Accuracy: {test_accuracy:.4f}")
print(f"📊 F1 Score (Macro): {test_f1_macro:.4f}")
print(f"📊 F1 Score (Weighted): {test_f1_weighted:.4f}")

# Classification report
target_names = [f"Class_{reverse_mapping[i]}" for i in range(num_classes)]
class_report = classification_report(test_true_labels, test_predictions, target_names=target_names)

print("\n📊 Classification Report:")
print(class_report)

# CELL 8: Save Results
print("\n💾 Saving Results...")

# Save model
model_save_path = "/kaggle/working/bert_labor_classifier_lightweight"
os.makedirs(model_save_path, exist_ok=True)

model.save_pretrained(model_save_path)
tokenizer.save_pretrained(model_save_path)

# Save reports
with open("/kaggle/working/classification_report_lightweight.txt", 'w') as f:
    f.write("BERT Labor Description Classification Report (Lightweight)\n")
    f.write("=" * 60 + "\n\n")
    f.write(f"Test Accuracy: {test_accuracy:.4f}\n")
    f.write(f"F1 Score (Macro): {test_f1_macro:.4f}\n")
    f.write(f"F1 Score (Weighted): {test_f1_weighted:.4f}\n\n")
    f.write("Classification Report:\n")
    f.write(class_report)

# Save metadata
metadata = {
    'model_type': 'BERT Lightweight',
    'test_accuracy': test_accuracy,
    'test_f1_macro': test_f1_macro,
    'test_f1_weighted': test_f1_weighted,
    'train_losses': train_losses,
    'val_accuracies': val_accuracies,
    'label_mapping': label_mapping,
    'reverse_mapping': reverse_mapping,
    'training_config': {
        'batch_size': BATCH_SIZE,
        'epochs': EPOCHS,
        'learning_rate': LEARNING_RATE,
        'max_length': MAX_LENGTH
    }
}

with open("/kaggle/working/model_metadata_lightweight.json", 'w') as f:
    json.dump(metadata, f, indent=2)

print("✅ All results saved!")
print(f"📁 Model: {model_save_path}")
print(f"📄 Report: /kaggle/working/classification_report_lightweight.txt")
print(f"📄 Metadata: /kaggle/working/model_metadata_lightweight.json")

print(f"\n🎉 LIGHTWEIGHT TRAINING COMPLETED!")
print(f"📊 Final Accuracy: {test_accuracy:.4f}")
print(f"📊 Final F1 (Macro): {test_f1_macro:.4f}")
print(f"📊 Final F1 (Weighted): {test_f1_weighted:.4f}")
